@extends('layouts.admin')

@section('title', 'Edit Reservation - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.show', $reservation) }}">Reservation
                            #{{ $reservation->id }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Edit Reservation Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-edit me-2"></i>Edit Reservation #{{ $reservation->id }}
                        <span
                            class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }} ms-2">
                            {{ $reservation->status }}
                        </span>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Details
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.update', $reservation) }}" id="reservationForm">
                        @csrf
                        @method('PUT')

                        <div class="row gy-4">
                            <!-- Left Column: Field Selection, Utilities, and Cost Overview -->
                            <div class="col-xl-6">
                                <!-- Field Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Field Selection
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @foreach ($fields as $field)
                                                        <option value="{{ $field->id }}"
                                                            data-rate="{{ $field->hourly_rate }}"
                                                            data-rate2="{{ $field->night_hourly_rate }}"
                                                            data-capacity="{{ $field->capacity }}"
                                                            data-type="{{ $field->type }}"
                                                            data-opening="{{ $field->opening_time }}"
                                                            data-closing="{{ $field->closing_time }}"
                                                            data-min-hours="{{ $field->min_booking_hours }}"
                                                            data-max-hours="{{ $field->max_booking_hours }}"
                                                            data-anochi="{{ $field->night_time_start }}"
                                                            {{ old('field_id', $reservation->field_id) == $field->id ? 'selected' : '' }}>
                                                            {{ $field->name }}
                                                            <!--
                                                                                                                        XCG {{ number_format($field->hourly_rate, 2) }}/hr -->
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo" class="alert alert-info">
                                                    <h6 class="fw-semibold">Field Information</h6>
                                                    <p class="mb-1"><strong>Type:</strong> <span
                                                            id="fieldType">{{ $reservation->field->type }}</span></p>
                                                    <p class="mb-1"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity">{{ $reservation->field->capacity }}</span>
                                                        people</p>
                                                    <p class="mb-1"><strong>Day Hourly Rate:</strong> XCG <span
                                                            id="fieldRate">{{ number_format($reservation->field->hourly_rate, 2) }}</span>
                                                    </p>
                                                    <p class="mb-1"><strong>Night Hourly Rate:</strong> XCG <span
                                                            id="fieldRate">{{ number_format($reservation->field->night_hourly_rate, 2) }}</span>
                                                    </p>
                                                    <p class="mb-1"><strong>Working Hours:</strong> <span
                                                            id="fieldHours">{{ $reservation->field->opening_time }} -
                                                            {{ $reservation->field->closing_time }}</span></p>
                                                    <p class="mb-0"><strong>Booking Duration:</strong> <span
                                                            id="fieldDuration">{{ $reservation->field->min_booking_hours }}
                                                            - {{ $reservation->field->max_booking_hours }}</span> hours</p>
                                                </div>
                                            </div>

                                            <!-- Date Selection -->
                                            <div class="col-xl-12">
                                                <label for="booking_date" class="form-label">Date <span
                                                        class="text-danger">*</span></label>
                                                <input type="date" name="booking_date" id="booking_date"
                                                    value="{{ old('booking_date', $reservation->booking_date->format('Y-m-d')) }}"
                                                    min="{{ date('Y-m-d') }}" required onchange="loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();"
                                                    class="form-control @error('booking_date') is-invalid @enderror">
                                                @error('booking_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Start Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="start_time" class="form-label">Start Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required
                                                    onchange="updateEndTimeOptions(); calculateCost(); checkUtilityPrerequisites(); enableProgressiveFields();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Start Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text text-muted" id="startTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a field first
                                                </div>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- End Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="end_time" class="form-label">End Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="end_time" id="end_time" required
                                                    onchange="calculateCost(); checkUtilityPrerequisites();"
                                                    class="form-select @error('end_time') is-invalid @enderror">
                                                    <option value="">Select End Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text text-muted" id="endTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a start time first
                                                </div>
                                                @error('end_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Utility Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Utility Selection (Optional)
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Utilities -->
                                            <div class="col-xl-12">
                                                <label class="form-label">Add Utility</label>

                                                <!-- Utility Prerequisites Message -->
                                                <div id="utilityPrerequisitesMessage" class="alert alert-info mb-3">
                                                    <i class="ti ti-info-circle me-2"></i>
                                                    <strong>Complete booking details first:</strong> Please select field, date, time, and duration before adding utilities.
                                                </div>

                                                <!-- Utility Selection Section -->
                                                <div id="utilitySelectionSection" class="utility-section-disabled">
                                                    <div class="row g-2 align-items-center mb-3">
                                                        <div class="col-md-5">
                                                            <select id="utilitySelect" class="form-select" disabled>
                                                                <option value="">Select Utility</option>
                                                                @foreach ($utilities as $utility)
                                                                    <option value="{{ $utility->id }}"
                                                                        data-name="{{ $utility->name }}"
                                                                        data-rate="{{ $utility->hourly_rate }}">
                                                                        {{ $utility->name }} -
                                                                        XCG {{ number_format($utility->hourly_rate, 2) }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <input type="number" id="utilityQuantity" class="form-control"
                                                                min="1" step="1" value="1" placeholder="Quantity" disabled>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button type="button" class="btn btn-primary w-100"
                                                                onclick="addUtility()" id="addUtilityBtn" disabled>Add</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <table class="table table-bordered" id="utilityTable">
                                                    <thead id="utilityTableHeader" class="d-none">
                                                        <tr>
                                                            <th>Utility</th>
                                                            <th>Quantity</th>
                                                            <th>Rate</th>
                                                            <th>Cost</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <!-- Existing utilities will be populated by JS -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <!-- end Utilities -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Total Cost Overview Section -->
                                <div class="card custom-card shadow-none border mb-4 d-none" id="costOverviewCard">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Total Cost Overview
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Availability Check -->
                                            <div class="col-xl-12">
                                                <div id="availabilityCheck" class="d-none">
                                                    <div id="availabilityMessage" class="alert"></div>
                                                </div>
                                            </div>
                                            <!------------------------------------------------------------------>
                                            <!-- Enhanced Cost Display with Detailed Breakdown -->
                                            <div class="col-xl-12">
                                                <!--<div id="costDisplay" class="alert alert-success d-none">
                                                                    <h6 class="fw-semibold">Reservation Cost</h6>
                                                                    <p class="mb-1"><strong>Total Cost: XCG <span
                                                                                id="totalCost">0.00</span></strong></p>
                                                                    <p class="mb-0 fs-12">Rate: XCG <span
                                                                            id="displayRate">0.00</span>/hour ×
                                                                        <span id="displayDuration">1</span> hour(s)
                                                                    </p>
                                                                </div>-->
                                                <div id="costDisplay" class="alert alert-success d-none">
                                                    <h6 class="fw-semibold">Reservation Cost Breakdown</h6>

                                                    <!-- Field Cost Breakdown -->
                                                    <div id="fieldCostBreakdown" class="mb-2">
                                                        <div class="fw-semibold mb-1">Field Cost:</div>
                                                        <div id="dayNightBreakdown" class="fs-12 text-muted mb-1">
                                                            <!-- Day/Night breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Field Total: XCG <span id="fieldCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Utility Cost Breakdown -->
                                                    <div id="utilityCostBreakdown" class="mb-2 d-none">
                                                        <div class="fw-semibold mb-1">Utility Costs:</div>
                                                        <div id="utilityDetails" class="fs-12 text-muted mb-1">
                                                            <!-- Utility breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Utility Total: XCG <span id="utilityCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Total Cost -->
                                                    <div class="border-top pt-2">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="fw-bold">Total Cost:</span>
                                                            <span class="h5 mb-0 text-success">XCG <span id="totalCost">0.00</span></span>
                                                        </div>
                                                    </div>

                                                    <!-- Server-side calculation notice -->
                                                    <div class="fs-11 text-muted mt-1">
                                                        <i class="ti ti-shield-check me-1"></i>Costs calculated securely by server
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column: Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">Customer Name <span
                                                        class="text-danger">*</span></label>
                                                <input type="text" name="customer_name" id="customer_name" required
                                                    value="{{ old('customer_name', $reservation->customer_name) }}"
                                                    placeholder="Name (required)"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email', $reservation->customer_email) }}"
                                                    placeholder="Email (optional)"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone <span
                                                        class="text-danger">*</span></label>
                                                <input type="tel" name="customer_phone" id="customer_phone" required
                                                    value="{{ old('customer_phone', $reservation->customer_phone) }}"
                                                    placeholder="Phone number (required)"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests', $reservation->special_requests) }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Current Reservation Info -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-warning">
                                                    <h6 class="fw-semibold">Current Reservation</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li><strong>Original Date:</strong>
                                                            {{ $reservation->booking_date->format('M d, Y') }}</li>
                                                        <li><strong>Original Time:</strong> {{ $reservation->time_range }}
                                                        </li>
                                                        <li><strong>Original Cost:</strong>
                                                            XCG {{ number_format($reservation->total_cost, 2) }}</li>
                                                        <li><strong>Status:</strong> {{ $reservation->status }}</li>
                                                    </ul>
                                                </div>
                                            </div>

                                            <!-- Modification Rules -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-info">
                                                    <h6 class="fw-semibold">Modification Rules</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li> Modifications must be made at least 24 hours in advance</li>
                                                        <li> New time slot must be available</li>
                                                        <li> Cost will be recalculated based on new selection</li>
                                                        <li> Reservation will remain confirmed after modification</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-end mt-4 pt-3 border-top">
                                    <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>Cancel
                                    </a>
                                    <button type="submit" id="submitBtn" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>Update Reservation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    /* Progressive Form Flow Styles */
    .utility-section-disabled {
        opacity: 0.6;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .utility-section-enabled {
        opacity: 1;
        pointer-events: auto;
        transition: opacity 0.3s ease;
    }

    #utilityPrerequisitesMessage {
        transition: all 0.3s ease;
    }

    #utilityPrerequisitesMessage.d-none {
        opacity: 0;
        transform: translateY(-10px);
    }

    /* Progressive Field Activation Styles */
    .form-select:disabled,
    .form-control:disabled {
        background-color: #f8f9fa;
        opacity: 0.65;
        cursor: not-allowed;
    }

    .form-text.text-muted {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text.text-muted i {
        color: #6c757d;
    }

    /* Form step indicators */
    .form-step-complete {
        position: relative;
    }

    .form-step-complete::after {
        content: '✓';
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #28a745;
        font-weight: bold;
        font-size: 16px;
    }

    /* Utility table header transition */
    #utilityTableHeader {
        transition: all 0.3s ease;
    }

    #utilityTableHeader.d-none {
        opacity: 0;
        transform: translateY(-10px);
    }
</style>
@endpush

@push('scripts')
    <script>
        // Store original values for comparison
        const originalValues = {
            field_id: '{{ $reservation->field_id }}',
            booking_date: '{{ $reservation->booking_date->format('Y-m-d') }}',
            start_time: '{{ $reservation->start_time }}',
            end_time: '{{ $reservation->end_time }}',
            duration_hours: '{{ $reservation->duration_hours }}'
        };

        // Global utilities array - moved here to be accessible by all functions
        let utilities = [];

        // Existing utilities data - moved here to be accessible by all functions
        const existingUtilities = @json($reservationUtilities);

        function updateFieldInfo() {
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const fieldInfo = document.getElementById('fieldInfo');

            if (selectedOption.value) {
                document.getElementById('fieldType').textContent = selectedOption.dataset.type || 'N/A';
                document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';
                document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                document.getElementById('fieldHours').textContent = (selectedOption.dataset.opening || '08:00') + ' - ' + (
                    selectedOption.dataset.closing || '22:00');
                document.getElementById('fieldDuration').textContent = (selectedOption.dataset.minHours || '1') + ' - ' + (
                    selectedOption.dataset.maxHours || '8') + ' hours';
                fieldInfo.classList.remove('d-none');
                calculateCost();
            } else {
                fieldInfo.classList.add('d-none');
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
            }
            checkUtilityPrerequisites(); // Check utility prerequisites when field changes
            enableProgressiveFields(); // Enable progressive field activation
        }

        // Progressive Field Activation Function
        function enableProgressiveFields() {
            const fieldSelect = document.getElementById('field_id');
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const startTimeHelp = document.getElementById('startTimeHelp');
            const endTimeHelp = document.getElementById('endTimeHelp');

            const fieldSelected = fieldSelect.value;
            const startTimeSelected = startTimeSelect.value;

            // For edit forms, check if we have original values to preserve
            const hasOriginalStartTime = originalValues && originalValues.start_time;
            const hasOriginalEndTime = originalValues && originalValues.end_time;

            // Enable/disable start time based on field selection
            if (fieldSelected) {
                startTimeSelect.disabled = false;
                startTimeHelp.innerHTML = '';
            } else {
                // Only disable if we don't have original values (new form)
                if (!hasOriginalStartTime) {
                    startTimeSelect.disabled = true;
                    startTimeSelect.innerHTML = '<option value="">Select Start Time</option>';
                    startTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a field first';
                }

                // Also disable end time if field is not selected and no original values
                if (!hasOriginalEndTime) {
                    endTimeSelect.disabled = true;
                    endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                    endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
                }
                return;
            }

            // Enable/disable end time based on start time selection
            if (startTimeSelected || hasOriginalStartTime) {
                endTimeSelect.disabled = false;
                endTimeHelp.innerHTML = '';
                // Update end time options if start time is selected
                if (startTimeSelected) {
                    updateEndTimeOptions();
                }
            } else {
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
            }
        }

        // New functions for start/end time logic
        function updateEndTimeOptions() {
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const endTimeHelp = document.getElementById('endTimeHelp');
            const fieldSelect = document.getElementById('field_id');
            const dateInput = document.getElementById('booking_date');

            const startTime = startTimeSelect.value;
            const selectedField = fieldSelect.options[fieldSelect.selectedIndex];
            const date = dateInput.value;

            if (!startTime || !selectedField.value || !date) {
                // Disable end time if no start time selected
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
                return;
            }

            // Show loading state
            endTimeSelect.disabled = true;
            endTimeSelect.innerHTML = '<option value="">Loading available times...</option>';
            endTimeHelp.innerHTML = '<i class="ti ti-loader me-1"></i>Checking availability...';

            // Store current end time for restoration
            const currentEndTime = originalValues.end_time || '{{ old("end_time", $reservation->end_time) }}';

            // Fetch available end times from server (excluding current reservation)
            fetch(`{{ route('reservations.available-end-times') }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    field_id: selectedField.value,
                    date: date,
                    start_time: startTime,
                    exclude_reservation_id: {{ $reservation->id }}
                })
            })
            .then(response => response.json())
            .then(data => {
                // Enable end time dropdown
                endTimeSelect.disabled = false;
                endTimeHelp.innerHTML = '';

                if (data.success && data.end_times.length > 0) {
                    // Clear and populate end time options
                    endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                    data.end_times.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.text;
                        endTimeSelect.appendChild(optionElement);
                    });

                    // Try to restore the current end time if it's available
                    if (currentEndTime) {
                        const endOption = endTimeSelect.querySelector(`option[value="${currentEndTime}"]`);
                        if (endOption) {
                            endTimeSelect.value = currentEndTime;
                            console.log('Successfully restored end time to:', currentEndTime);
                        } else {
                            console.warn('Original end time not available for:', currentEndTime);
                            // Add the original time as a special option if it's not in the available list
                            const originalOption = document.createElement('option');
                            originalOption.value = currentEndTime;
                            originalOption.textContent = `${currentEndTime} (Current - may conflict)`;
                            originalOption.selected = true;
                            originalOption.style.color = '#dc3545'; // Red color to indicate potential issue
                            endTimeSelect.appendChild(originalOption);
                            console.log('Added original end time as special option:', currentEndTime);
                        }
                    }
                } else {
                    // No available end times
                    endTimeSelect.innerHTML = '<option value="">No available end times</option>';
                    endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-warning"></i>No available end times for this start time';

                    // If we have a current end time, still add it as an option
                    if (currentEndTime) {
                        const originalOption = document.createElement('option');
                        originalOption.value = currentEndTime;
                        originalOption.textContent = `${currentEndTime} (Current - conflicts detected)`;
                        originalOption.selected = true;
                        originalOption.style.color = '#dc3545';
                        endTimeSelect.appendChild(originalOption);
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching available end times:', error);
                endTimeSelect.disabled = false;
                endTimeSelect.innerHTML = '<option value="">Error loading times</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-danger"></i>Error loading available times';

                // Fallback: add current end time if available
                if (currentEndTime) {
                    const fallbackOption = document.createElement('option');
                    fallbackOption.value = currentEndTime;
                    fallbackOption.textContent = `${currentEndTime} (Current)`;
                    fallbackOption.selected = true;
                    endTimeSelect.appendChild(fallbackOption);
                }
            });
        }



        function calculateDurationFromTimes(startTime, endTime) {
            if (!startTime || !endTime) return 0;

            const startDate = new Date(`2000-01-01 ${startTime}`);
            const endDate = new Date(`2000-01-01 ${endTime}`);

            if (endDate <= startDate) return 0;

            return (endDate - startDate) / (1000 * 60 * 60); // Duration in hours
        }

        function formatTimeDisplay(timeString) {
            const time = new Date(`2000-01-01 ${timeString}`);
            return time.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }

        function formatDuration(hours) {
            if (hours === 0.5) return '30 min';
            if (hours === 1) return '1 hour';
            if (hours % 1 === 0) return `${hours} hours`;
            return `${hours} hours`;
        }

        // Legacy client-side calculation removed - now using server-side authoritative calculation

        /////////////////////////////////////////////////////////////////////////////////////////////////
        // Client-side cost calculation removed - using server-side authoritative calculation
        // Performance optimization: debounce and request deduplication
        let costCalculationTimeout;
        let lastRequestData = null;
        let currentRequest = null;

        function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const rate = parseFloat(selectedOption.dataset.rate || 0);

            const fieldId = fieldSelect.value;
            let startTime = startTimeSelect.value;
            let endTime = endTimeSelect.value;

            // Fallback: If times are not selected but we have original values, use them
            if (!startTime && originalValues.start_time) {
                startTime = originalValues.start_time;
                console.log('Using original start time for cost calculation:', startTime);
            }
            if (!endTime && originalValues.end_time) {
                endTime = originalValues.end_time;
                console.log('Using original end time for cost calculation:', endTime);
            }

            if (!fieldId || !startTime || !endTime) {
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
                return;
            }

            // Calculate duration from start and end times
            const duration = calculateDurationFromTimes(startTime, endTime);
            if (duration <= 0) {
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
                return;
            }

            // Prepare utilities data for server calculation
            const utilitiesData = utilities.map(u => ({
                id: u.id,
                hours: u.quantity
            }));

            // Debug: Log cost calculation request
            console.log('Cost calculation triggered:', {
                fieldId,
                startTime,
                endTime,
                duration,
                utilities: utilitiesData,
                utilitiesCount: utilities.length,
                utilitiesArray: utilities
            });

            // Create request data for comparison
            const requestData = {
                field_id: fieldId,
                start_time: startTime,
                end_time: endTime,
                utilities: utilitiesData
            };

            // Check if request data has changed (avoid redundant requests)
            const requestDataString = JSON.stringify(requestData);
            if (lastRequestData === requestDataString) {
                return; // No change, skip request
            }

            // Cancel previous timeout
            if (costCalculationTimeout) {
                clearTimeout(costCalculationTimeout);
            }

            // Debounce the request (300ms delay)
            costCalculationTimeout = setTimeout(() => {
                // Cancel previous request if still pending
                if (currentRequest) {
                    currentRequest.abort();
                }

                // Store current request data
                lastRequestData = requestDataString;

                // Create AbortController for request cancellation
                const controller = new AbortController();
                currentRequest = controller;

                // Fetch cost calculation from server
                fetch(`{{ route('reservations.cost-estimate') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field_id: fieldId,
                        start_time: startTime,
                        end_time: endTime,
                        utilities: utilitiesData
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Debug: Log server response
                    console.log('Server response received:', {
                        total_cost: data.total_cost,
                        field_cost: data.field_cost,
                        utility_breakdown_count: data.utility_breakdown ? data.utility_breakdown.length : 0,
                        full_response: data
                    });

                    if (data.error) {
                        console.error('Cost calculation error:', data.error);
                        if (data.validation_errors) {
                            console.error('Validation errors:', data.validation_errors);
                        }
                        return;
                    }

                // Update UI with server-calculated detailed breakdown
                const totalCost = parseFloat(data.total_cost || 0);
                document.getElementById('totalCost').textContent = totalCost.toFixed(2);

                // Update field cost breakdown
                const fieldCost = parseFloat(data.field_cost || data.subtotal || data.total_cost || 0);
                document.getElementById('fieldCost').textContent = fieldCost.toFixed(2);

                // Update day/night rate breakdown
                const dayNightBreakdown = document.getElementById('dayNightBreakdown');
                if (data.rate_breakdown && (data.rate_breakdown.day_hours > 0 || data.rate_breakdown.night_hours > 0)) {
                    let breakdownHtml = '';
                    if (data.rate_breakdown.day_hours > 0) {
                        const dayRate = parseFloat(data.hourly_rate || 0);
                        const dayCost = parseFloat(data.rate_breakdown.day_cost || 0);
                        breakdownHtml += `Day Rate: ${data.rate_breakdown.day_hours} hours × XCG ${dayRate.toFixed(2)} = XCG ${dayCost.toFixed(2)}<br>`;
                    }
                    if (data.rate_breakdown.night_hours > 0) {
                        const nightRate = parseFloat(data.night_hourly_rate || 0);
                        const nightCost = parseFloat(data.rate_breakdown.night_cost || 0);
                        breakdownHtml += `Night Rate: ${data.rate_breakdown.night_hours} hours × XCG ${nightRate.toFixed(2)} = XCG ${nightCost.toFixed(2)}`;
                    }
                    dayNightBreakdown.innerHTML = breakdownHtml;
                } else {
                    // Simple rate display for fields without night rates
                    dayNightBreakdown.innerHTML = `${duration} hours × XCG ${rate.toFixed(2)} = XCG ${fieldCost.toFixed(2)}`;
                }

                // Update utility cost breakdown
                const utilityCostBreakdown = document.getElementById('utilityCostBreakdown');
                const utilityDetails = document.getElementById('utilityDetails');
                const utilityCostSpan = document.getElementById('utilityCost');

                console.log('Processing utility breakdown:', {
                    hasUtilityBreakdown: !!data.utility_breakdown,
                    utilityBreakdownLength: data.utility_breakdown ? data.utility_breakdown.length : 0,
                    utilitiesInForm: utilities.length
                });

                if (data.utility_breakdown && data.utility_breakdown.length > 0) {
                    let utilityHtml = '';
                    let totalUtilityCost = 0;

                    data.utility_breakdown.forEach(utility => {
                        const utilityRate = parseFloat(utility.rate || 0);
                        const utilityCost = parseFloat(utility.cost || 0);
                        utilityHtml += `${utility.name}: ${utility.hours} × XCG ${utilityRate.toFixed(2)} = XCG ${utilityCost.toFixed(2)}<br>`;
                        totalUtilityCost += utilityCost;

                        // Update utility costs in table with multiple methods
                        // Method 1: Direct cost cell ID (most reliable)
                        const costCellById = document.getElementById(`utility-cost-${utility.utility_id}`);

                        if (costCellById) {
                            costCellById.innerHTML = `XCG ${utilityCost.toFixed(2)}`;
                            console.log(`✅ Utility cost updated: ${utility.name} = XCG ${utilityCost.toFixed(2)}`);
                            return; // Success, no need for fallback methods
                        }

                        // Method 2: Row data attribute
                        let utilityRow = document.querySelector(`#utilityTable tbody tr[data-utility-id="${utility.utility_id}"]`);

                        // Method 3: Try finding by hidden input name
                        if (!utilityRow) {
                            const rows = document.querySelectorAll('#utilityTable tbody tr');
                            rows.forEach(row => {
                                const hiddenInput = row.querySelector(`input[name="utilities[${utility.utility_id}][id]"]`);
                                if (hiddenInput) {
                                    utilityRow = row;
                                }
                            });
                        }

                        // Method 4: Try finding by utility name (less reliable but better than nothing)
                        if (!utilityRow) {
                            const rows = document.querySelectorAll('#utilityTable tbody tr');
                            rows.forEach(row => {
                                const nameCell = row.cells[0];
                                if (nameCell && nameCell.textContent.trim() === utility.name) {
                                    utilityRow = row;
                                }
                            });
                        }

                        if (utilityRow) {
                            const costCell = utilityRow.cells[3]; // Cost column (0: name, 1: quantity, 2: rate, 3: cost, 4: action)
                            if (costCell) {
                                costCell.innerHTML = `XCG ${utilityCost.toFixed(2)}`;
                                console.log(`✅ Utility cost updated (fallback): ${utility.name} = XCG ${utilityCost.toFixed(2)}`);
                            }
                        } else {
                            console.warn(`⚠️ Could not find utility row for ${utility.name} (ID: ${utility.utility_id})`);
                        }
                    });

                    utilityDetails.innerHTML = utilityHtml;
                    utilityCostSpan.textContent = totalUtilityCost.toFixed(2);
                    utilityCostBreakdown.classList.remove('d-none');
                } else {
                    utilityCostBreakdown.classList.add('d-none');
                    utilityCostSpan.textContent = '0.00';
                }

                // Ensure success styling and show cost overview card
                const costDisplay = document.getElementById('costDisplay');
                costDisplay.className = 'alert alert-success d-block';
                costDisplay.classList.remove('d-none');
                document.getElementById('costOverviewCard').classList.remove('d-none');
            })
            .catch(error => {
                console.error('Error calculating cost:', error);
                // Show error state instead of client-side calculation
                document.getElementById('totalCost').textContent = 'Error';
                document.getElementById('fieldCost').textContent = 'Error';
                document.getElementById('costOverviewCard').classList.remove('d-none');

                // Show user-friendly error message
                const costDisplay = document.getElementById('costDisplay');
                costDisplay.className = 'alert alert-warning d-block';
                costDisplay.classList.remove('d-none');
                costDisplay.innerHTML = `
                    <h6 class="fw-semibold">Cost Calculation Unavailable</h6>
                    <p class="mb-0">Unable to calculate cost at this time. Please try again or contact support.</p>
                `;
            });
            }, 300); // Close setTimeout
        }
        /////////////////////////////////////////////////////////////////////////////////////////////////

        function loadAvailability() {
            const fieldId = document.getElementById('field_id').value;
            const date = document.getElementById('booking_date').value;

            if (!fieldId || !date) {
                return;
            }

            // Get field constraints for minimum duration
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const minDuration = parseFloat(selectedOption.dataset.minHours || 0.5);

            console.log('Loading availability for editing with min duration:', minDuration);

            // Load available start time slots (using minimum duration for initial availability)
            fetch(`{{ route('reservations.check-availability') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field_id: fieldId,
                        date: date,
                        duration_hours: minDuration,
                        exclude_reservation_id: {{ $reservation->id }}
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Received availability data:', data);
                    updateTimeSlots(data.slots || []);
                })
                .catch(error => {
                    console.error('Error loading availability:', error);
                });
        }

        function updateTimeSlots(slots) {
            const timeSelect = document.getElementById('start_time');
            const currentValue = timeSelect.value || originalValues.start_time;

            // Clear existing options (including any fallback options)
            timeSelect.innerHTML = '<option value="">Select Time</option>';

            // Add available slots
            let timeSelected = false;
            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.start_time;
                // Show only the time value for start time (e.g., "13:00")
                option.textContent = slot.start_time;
                if (slot.start_time === currentValue) {
                    option.selected = true;
                    timeSelected = true;
                }
                timeSelect.appendChild(option);
            });

            // If current time wasn't found in available slots, add it as a special option
            if (currentValue && !timeSelected) {
                const currentOption = document.createElement('option');
                currentOption.value = currentValue;
                currentOption.textContent = `${currentValue} (Current - may not be available)`;
                currentOption.selected = true;
                currentOption.style.color = '#dc3545'; // Red color to indicate issue
                timeSelect.appendChild(currentOption);
            }

            // If original time is not available, show warning
            if (currentValue && !slots.find(slot => slot.start_time === currentValue)) {
                const availabilityCheck = document.getElementById('availabilityCheck');
                const availabilityMessage = document.getElementById('availabilityMessage');
                availabilityMessage.className = 'alert alert-warning';
                availabilityMessage.textContent = 'Original time slot is no longer available. Please select a new time.';
                availabilityCheck.classList.remove('d-none');
            } else {
                document.getElementById('availabilityCheck').classList.add('d-none');
            }

            // If we have a start time selected (either from form or original value), update end time options
            const startTimeSelect = document.getElementById('start_time');
            if (startTimeSelect.value) {
                console.log('Start time is set, updating end time options...');
                updateEndTimeOptions();
            }

            // Check utility prerequisites after time slots are updated
            checkUtilityPrerequisites();

            // IMPORTANT: Calculate cost after time slots are populated
            // This ensures the start_time value is available for cost calculation
            calculateCost();
        }

        // Progressive Form Flow Functions
        function checkUtilityPrerequisites() {
            const fieldId = document.getElementById('field_id').value;
            const bookingDate = document.getElementById('booking_date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;

            const allRequiredFieldsSelected = fieldId && bookingDate && startTime && endTime;

            const utilitySelect = document.getElementById('utilitySelect');
            const utilityQuantity = document.getElementById('utilityQuantity');
            const addUtilityBtn = document.getElementById('addUtilityBtn');
            const utilitySection = document.getElementById('utilitySelectionSection');
            const prerequisitesMessage = document.getElementById('utilityPrerequisitesMessage');

            if (allRequiredFieldsSelected) {
                // Enable utilities section
                utilitySelect.disabled = false;
                utilityQuantity.disabled = false;
                addUtilityBtn.disabled = false;
                utilitySection.className = 'utility-section-enabled';
                prerequisitesMessage.classList.add('d-none');

                // Add visual indicators to completed fields
                addFormStepIndicator('field_id');
                addFormStepIndicator('booking_date');
                addFormStepIndicator('start_time');
                addFormStepIndicator('end_time');
            } else {
                // Disable utilities section
                utilitySelect.disabled = true;
                utilityQuantity.disabled = true;
                addUtilityBtn.disabled = true;
                utilitySection.className = 'utility-section-disabled';
                prerequisitesMessage.classList.remove('d-none');

                // Update prerequisites message with specific missing fields
                updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime);

                // Remove visual indicators from incomplete fields
                removeFormStepIndicator('field_id');
                removeFormStepIndicator('booking_date');
                removeFormStepIndicator('start_time');
                removeFormStepIndicator('end_time');
            }
        }

        function updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime) {
            const missingFields = [];
            if (!fieldId) missingFields.push('field');
            if (!bookingDate) missingFields.push('date');
            if (!startTime) missingFields.push('start time');
            if (!endTime) missingFields.push('end time');

            const message = document.getElementById('utilityPrerequisitesMessage');
            if (missingFields.length > 0) {
                const fieldList = missingFields.join(', ');
                message.innerHTML = `
                    <i class="ti ti-info-circle me-2"></i>
                    <strong>Complete booking details first:</strong> Please select ${fieldList} before adding utilities.
                `;
            }
        }

        function addFormStepIndicator(fieldId) {
            const field = document.getElementById(fieldId);
            if (field && field.value) {
                field.classList.add('form-step-complete');
            }
        }

        function removeFormStepIndicator(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.remove('form-step-complete');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // First, update field info to populate field-dependent data
            updateFieldInfo();

            // Initialize progressive field activation
            enableProgressiveFields();

            // Load existing utilities first (before cost calculation)
            console.log('Loading existing utilities:', existingUtilities);
            if (Array.isArray(existingUtilities)) {
                existingUtilities.forEach(util => {
                    console.log('Adding existing utility:', util);
                    addUtility(util.id, util.name, parseFloat(util.rate), parseInt(util.hours), false);
                });
                console.log('Utilities array after loading existing:', utilities);
            } else {
                console.log('No existing utilities to load or existingUtilities is not an array');
            }

            // Load availability to populate time slots (this will preserve the current time)
            // Cost calculation will be triggered automatically after time slots are loaded
            loadAvailability();

            // Set up end time options after a longer delay to ensure start time is properly populated
            setTimeout(function() {
                console.log('Setting up end time options for editing...');
                const startTimeSelect = document.getElementById('start_time');
                const endTimeSelect = document.getElementById('end_time');

                console.log('Current start time value:', startTimeSelect.value);
                console.log('Original start time:', originalValues.start_time);
                console.log('Original end time:', originalValues.end_time);

                // Ensure start time is set to original value if not already set
                if (!startTimeSelect.value && originalValues.start_time) {
                    const startOption = startTimeSelect.querySelector(`option[value="${originalValues.start_time}"]`);
                    if (startOption) {
                        startTimeSelect.value = originalValues.start_time;
                        console.log('Set start time to original value:', originalValues.start_time);
                    }
                }

                // Update end time options and set current value
                updateEndTimeOptions();

                // Additional delay to ensure end time options are generated before setting value
                setTimeout(function() {
                    if (originalValues.end_time && !endTimeSelect.value) {
                        const endOption = endTimeSelect.querySelector(`option[value="${originalValues.end_time}"]`);
                        if (endOption) {
                            endTimeSelect.value = originalValues.end_time;
                            console.log('Set end time to original value:', originalValues.end_time);
                        } else {
                            console.warn('End time option not found for:', originalValues.end_time);
                        }
                    }

                    // Trigger cost calculation after both times are set
                    calculateCost();
                    checkUtilityPrerequisites();
                    enableProgressiveFields();
                }, 200);
            }, 800);

            // Fallback: If cost calculation hasn't been triggered after 2 seconds, trigger it manually
            // This handles cases where loadAvailability might fail or take too long
            setTimeout(function() {
                const costDisplay = document.getElementById('costDisplay');
                if (costDisplay.classList.contains('d-none')) {
                    console.log('Fallback: Triggering cost calculation after timeout');
                    calculateCost();
                }
            }, 2000);

            // Initialize utility prerequisites check
            checkUtilityPrerequisites();
        });
    </script>


    <script>
        /////////////////////////////////// di utilities
        // utilities array is now declared in the first script tag

        function addUtility(id = null, name = '', rate = 0, quantity = 1, triggerCalculation = true) {
            const tableBody = document.querySelector('#utilityTable tbody');
            const tableHeader = document.getElementById('utilityTableHeader');

            if (!id) {
                const select = document.getElementById('utilitySelect');
                const quantityInput = document.getElementById('utilityQuantity');

                id = select.value;
                name = select.options[select.selectedIndex].dataset.name;
                rate = parseFloat(select.options[select.selectedIndex].dataset.rate || 0);
                quantity = parseInt(quantityInput.value || 1);

                if (!id || quantity <= 0 || !Number.isInteger(quantity)) {
                    alert("Please select a utility and enter a valid whole number quantity.");
                    return;
                }

                if (utilities.find(u => u.id === id)) {
                    alert("This utility is already added.");
                    return;
                }

                // Reset form inputs only when adding from UI
                select.selectedIndex = 0;
                quantityInput.value = 1;
            }

            if (utilities.find(u => u.id === id)) return;

            utilities.push({
                id,
                name,
                rate,
                quantity
            });

            const row = document.createElement('tr');
            row.setAttribute('data-utility-id', id);
            row.id = `utility-row-${id}`; // Add unique ID for more reliable selection
            row.innerHTML = `
        <td>${name}</td>
        <td class="utility-hours">${quantity}</td>
        <td class="utility-rate">${rate.toFixed(2)}</td>
        <td id="utility-cost-${id}"><span class="text-muted">Calculating...</span></td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeUtility('${id}', this)">Remove</button>
        </td>
        <input type="hidden" name="utilities[${id}][id]" value="${id}">
        <input type="hidden" name="utilities[${id}][hours]" value="${quantity}">
    `;

            tableBody.appendChild(row);

            // Show table header when first utility is added
            if (utilities.length === 1) {
                tableHeader.classList.remove('d-none');
            }

            // Only trigger cost calculation if requested (avoid multiple calls during initial load)
            if (triggerCalculation) {
                calculateCost();
            }
        }



        function removeUtility(id, btn) {
            utilities = utilities.filter(u => u.id !== id);
            btn.closest('tr').remove();

            // Hide table header when no utilities remain
            const tableHeader = document.getElementById('utilityTableHeader');
            if (utilities.length === 0) {
                tableHeader.classList.add('d-none');
            }

            calculateCost();
        }

        // Duplicate calculateCost function removed - using server-side calculation above
    </script>

    <!-- existingUtilities is now declared in the first script tag -->
@endpush

<?php

namespace Tests\Feature\General\Calendar;

use App\Http\Controllers\CalendarController;
use App\Mail\ReservationUpdated;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(CalendarController::class)]
class CalendarControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;

    protected User $user;

    protected Field $fieldA;

    protected Field $fieldB;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->user = User::factory()->create(['role' => 'member']);

        $this->fieldA = Field::factory()->active()->create([
            'name' => 'Alpha Field',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);
        $this->fieldB = Field::factory()->active()->create([
            'name' => 'Beta Field',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);
    }

    // ============================
    // index()
    // ============================

    #[Test]
    public function index_displays_calendar_with_fields()
    {
        $response = $this->actingAs($this->user)->get(route('calendar.index'));

        $response->assertStatus(200)
            ->assertViewIs('calendar.index')
            ->assertViewHas('fields');

        $fields = $response->viewData('fields');
        $this->assertGreaterThanOrEqual(2, $fields->count());
    }

    // ============================
    // events()
    // ============================

    #[Test]
    public function events_returns_events_in_date_range_and_excludes_cancelled()
    {
        $date = Carbon::now()->addDays(1)->toDateString();
        $start = Carbon::now()->addDay()->subDay()->toDateString(); // yesterday
        $end = Carbon::now()->addDay()->addDay()->toDateString();   // tomorrow

        // In range (same day)
        $r1 = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);
        $r2 = Reservation::factory()->for($this->fieldB, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '14:00',
            'end_time' => '16:00',
            'status' => 'Pending',
        ]);
        // Excluded by status
        $r3 = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '09:00',
            'end_time' => '10:00',
            'status' => 'Cancelled',
        ]);
        // Out of range
        $r4 = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => Carbon::now()->addDays(5)->toDateString(),
            'start_time' => '09:00',
            'end_time' => '10:00',
            'status' => 'Confirmed',
        ]);

        $response = $this->actingAs($this->user)->getJson(route('calendar.events', [
            'start' => $start,
            'end' => $end,
        ]));

        $response->assertOk();
        $events = collect($response->json());

        // Assert r1 and r2 present, r3/r4 excluded
        $ids = $events->pluck('id')->all();
        $this->assertContains($r1->id, $ids);
        $this->assertContains($r2->id, $ids);
        $this->assertNotContains($r3->id, $ids);
        $this->assertNotContains($r4->id, $ids);

        $response->assertJsonStructure([
            '*' => [
                'id', 'title', 'start', 'end', 'color', 'textColor', 'extendedProps' => [
                    'reservation_id', 'field_id', 'field_name', 'customer_name', 'status', 'total_cost', 'duration', 'can_edit', 'special_requests',
                ],
                'url',
            ],
        ]);
    }

    #[Test]
    public function events_filters_by_field_id()
    {
        $date = Carbon::now()->addDays(3)->toDateString();

        $rA = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '11:00',
            'status' => 'Confirmed',
        ]);
        $rB = Reservation::factory()->for($this->fieldB, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '12:00',
            'end_time' => '13:00',
            'status' => 'Confirmed',
        ]);

        $response = $this->actingAs($this->user)->getJson(route('calendar.events', [
            'start' => Carbon::parse($date)->subDay()->toDateString(),
            'end' => Carbon::parse($date)->addDay()->toDateString(),
            'field_id' => $this->fieldA->id,
        ]));

        $response->assertOk();
        $events = collect($response->json());
        // Only events for fieldA should be returned
        $this->assertTrue($events->count() >= 1, 'Expected at least one event for field A');
        foreach ($events as $event) {
            $this->assertEquals($this->fieldA->id, $event['extendedProps']['field_id']);
        }
    }

    #[Test]
    public function events_sets_color_by_status_and_can_edit_for_admin_and_owner()
    {
        $date = Carbon::now()->addDays(4)->toDateString();

        $pending = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '09:00',
            'end_time' => '10:00',
            'status' => 'Pending',
        ]);
        $confirmed = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '11:00',
            'status' => 'Confirmed',
        ]);
        $completed = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $date,
            'start_time' => '11:00',
            'end_time' => '12:00',
            'status' => 'Completed',
        ]);

        // Admin sees can_edit true for all (per controller logic)
        $adminResp = $this->actingAs($this->admin)->getJson(route('calendar.events', [
            'start' => $date,
            'end' => $date,
        ]));
        $adminResp->assertOk();
        foreach ($adminResp->json() as $event) {
            $this->assertTrue($event['extendedProps']['can_edit']);
        }

        // Owner sees can_edit only if canBeCancelled (24h cutoff)
        // Create one within 12 hours (not editable)
        $soon = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => Carbon::now()->addHours(12)->toDateString(),
            'start_time' => '13:00', 'end_time' => '14:00', 'status' => 'Confirmed',
        ]);
        $ownerResp = $this->actingAs($this->user)->getJson(route('calendar.events', [
            'start' => Carbon::now()->toDateString(),
            'end' => Carbon::now()->addDays(5)->toDateString(),
        ]));
        $ownerResp->assertOk();
        $events = collect($ownerResp->json());

        $map = $events->keyBy('id');
        $this->assertEquals('#fbbf24', $map[$pending->id]['color']);
        $this->assertEquals('#23b7e5', $map[$confirmed->id]['color']);
        $this->assertEquals('#8c9097', $map[$completed->id]['color']);
        $this->assertFalse($map[$soon->id]['extendedProps']['can_edit']);
    }

    #[Test]
    public function events_marks_past_reservations_with_muted_colors_and_past_flag()
    {
        // Create a past reservation (yesterday)
        $pastDate = Carbon::now()->subDay()->toDateString();
        $pastReservation = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $pastDate,
            'start_time' => '10:00',
            'end_time' => '11:00',
            'status' => 'Completed',
        ]);

        // Create a future reservation (tomorrow)
        $futureDate = Carbon::now()->addDay()->toDateString();
        $futureReservation = Reservation::factory()->for($this->fieldA, 'field')->for($this->user, 'user')->create([
            'booking_date' => $futureDate,
            'start_time' => '10:00',
            'end_time' => '11:00',
            'status' => 'Confirmed',
        ]);

        $response = $this->actingAs($this->user)->getJson(route('calendar.events', [
            'start' => Carbon::now()->subDays(2)->toDateString(),
            'end' => Carbon::now()->addDays(2)->toDateString(),
        ]));

        $response->assertOk();
        $events = $response->json();

        // Find the events by reservation ID
        $pastEvent = collect($events)->firstWhere('extendedProps.reservation_id', $pastReservation->id);
        $futureEvent = collect($events)->firstWhere('extendedProps.reservation_id', $futureReservation->id);

        // Assert past reservation has muted color and is marked as past
        $this->assertNotNull($pastEvent);
        $this->assertEquals('#8c9097', $pastEvent['color']);
        $this->assertTrue($pastEvent['extendedProps']['is_past']);

        // Assert future reservation has normal color and is not marked as past
        $this->assertNotNull($futureEvent);
        $this->assertEquals('#23b7e5', $futureEvent['color']);
        $this->assertFalse($futureEvent['extendedProps']['is_past']);
    }

    #[Test]
    public function events_returns_500_on_invalid_dates()
    {
        $response = $this->actingAs($this->user)->getJson(route('calendar.events', [
            'start' => 'not-a-date',
            'end' => 'also-bad',
        ]));

        $response->assertStatus(500)
            ->assertJson(['error' => 'Failed to fetch calendar events']);
    }

    // ============================
    // updateReservationDate()
    // ============================

    #[Test]
    public function update_reservation_date_succeeds_for_admin_and_sends_emails()
    {
        Mail::fake();

        $owner = User::factory()->create(['role' => 'user']);
        $reservation = Reservation::factory()->for($this->fieldA, 'field')->for($owner, 'user')->create([
            'booking_date' => Carbon::now()->addDays(3)->toDateString(),
            'customer_email' => '<EMAIL>',
        ]);

        $newStart = Carbon::now()->addDays(5)->format('Y-m-d H:i:s');

        $resp = $this->actingAs($this->admin)->postJson(route('calendar.update-reservation', $reservation), [
            'start' => $newStart,
        ]);

        $resp->assertOk()->assertJson(['message' => 'Reservation updated successfully']);
        // booking_date is cast to date in model, but DB may store full datetime string
        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
        ]);
        $this->assertSame(Carbon::parse($newStart)->toDateString(), $reservation->fresh()->booking_date->toDateString());

        Mail::assertSent(ReservationUpdated::class, 3); // to user, customer(if set), and admin address
    }

    #[Test]
    public function update_reservation_date_is_unauthorized_for_non_owner_non_admin()
    {
        $owner = User::factory()->create(['role' => 'user']);
        $intruder = User::factory()->create(['role' => 'user']);

        $reservation = Reservation::factory()->for($this->fieldA, 'field')->for($owner, 'user')->create([
            'booking_date' => Carbon::now()->addDays(3)->toDateString(),
        ]);

        $resp = $this->actingAs($intruder)->postJson(route('calendar.update-reservation', $reservation), [
            'start' => Carbon::now()->addDays(4)->format('Y-m-d H:i:s'),
        ]);

        $resp->assertStatus(403)->assertJson(['message' => 'Unauthorized']);
    }

    #[Test]
    public function update_reservation_date_validates_start_is_required_date()
    {
        $owner = User::factory()->create(['role' => 'user']);
        $reservation = Reservation::factory()->for($this->fieldA, 'field')->for($owner, 'user')->create([
            'booking_date' => Carbon::now()->addDays(3)->toDateString(),
        ]);

        // Missing start
        $resp1 = $this->actingAs($owner)->postJson(route('calendar.update-reservation', $reservation), []);
        $resp1->assertStatus(422)->assertJsonValidationErrors(['start']);

        // Invalid start format
        $resp2 = $this->actingAs($owner)->postJson(route('calendar.update-reservation', $reservation), [
            'start' => 'invalid-date',
        ]);
        $resp2->assertStatus(422)->assertJsonValidationErrors(['start']);
    }
}

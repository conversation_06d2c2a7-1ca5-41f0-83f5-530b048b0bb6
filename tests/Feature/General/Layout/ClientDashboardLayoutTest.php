<?php

namespace Tests\Feature\General\Layout;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(User::class)]
class ClientDashboardLayoutTest extends TestCase
{
    use RefreshDatabase;

    protected User $clientUser;

    protected User $member;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clientUser = User::factory()->create([
            'name' => 'Client User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $this->member = User::factory()->create([
            'name' => 'Member',
            'email' => '<EMAIL>',
            'role' => 'member',
        ]);

        $this->adminUser = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
    }

    #[Test]
    public function client_dashboard_loads_successfully()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200)
            ->assertViewIs('user.dashboard')
            ->assertSee('User Dashboard')
            ->assertSee('Welcome back, Client User!');
    }

    #[Test]
    public function client_dashboard_has_proper_layout_structure()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200)
            // Check for welcome section with proper structure
            ->assertSee('Welcome back, Client User!')
            ->assertSee('SMP Online')

            // Check for Quick Actions section
            ->assertSee('Quick Actions')
            ->assertSee('View Profile')
            ->assertSee('Edit Settings')
            ->assertSee('Security')
            ->assertSee('Support')

            // Check for Features Grid
            ->assertSee('Profile Management')
            ->assertSee('Account Security')

            // Check for Account Information
            ->assertSee('Your Account Information')
            ->assertSee('User Access Level');
    }

    #[Test]
    public function client_dashboard_shows_appropriate_buttons_with_proper_spacing()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        // Verify that buttons are in dedicated sections, not next to user name
        $content = $response->getContent();

        // Check that user name is in welcome section
        $this->assertStringContainsString('Welcome back, Client User!', $content);

        // Check that action buttons are in separate Quick Actions section
        $this->assertStringContainsString('Quick Actions', $content);
        $this->assertStringContainsString('btn btn-outline-primary w-100', $content);

        // Verify proper card structure separates user name from action buttons
        $this->assertStringContainsString('card custom-card', $content);
    }

    #[Test]
    public function client_header_dropdown_shows_appropriate_links()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        // Client should see profile and settings, but NOT reservation links
        $response->assertSee('Profile')
            ->assertSee('My Profile')
            ->assertSee('Settings')
            ->assertDontSee('Reservations')
            ->assertDontSee('New Reservation');

        // Check that Features navigation link is not present (but allow "features" in general text)
        $content = $response->getContent();
        $this->assertStringNotContainsString('href="'.route('member.features').'"', $content);
    }

    #[Test]
    public function normal_user_header_dropdown_shows_reservation_links()
    {
        $response = $this->actingAs($this->member)
            ->get(route('member.dashboard'));

        $response->assertStatus(200);

        // Member should see reservation links
        $response->assertSee('Profile')
            ->assertSee('Reservations')
            ->assertSee('New Reservation')
            ->assertSee('Features');
    }

    #[Test]
    public function admin_header_dropdown_shows_reservation_links()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.dashboard'));

        $response->assertStatus(200);

        // Admin should see reservation links
        $response->assertSee('Profile')
            ->assertSee('Reservations')
            ->assertSee('New Reservation')
            ->assertSee('Features');
    }

    #[Test]
    public function client_dashboard_has_responsive_layout()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check for responsive Bootstrap classes
        $this->assertStringContainsString('col-xl-12', $content);
        $this->assertStringContainsString('col-xl-6', $content);
        $this->assertStringContainsString('col-lg-6', $content);
        $this->assertStringContainsString('col-md-6', $content);
        $this->assertStringContainsString('col-xl-3 col-lg-4 col-md-6', $content);
    }

    #[Test]
    public function client_dashboard_quick_actions_are_properly_spaced()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that Quick Actions section has proper spacing classes
        $this->assertStringContainsString('row gy-3', $content);
        $this->assertStringContainsString('btn btn-outline-primary w-100', $content);
        $this->assertStringContainsString('btn btn-outline-success w-100', $content);
        $this->assertStringContainsString('btn btn-outline-info w-100', $content);
        $this->assertStringContainsString('btn btn-outline-warning w-100', $content);
    }

    #[Test]
    public function client_dashboard_feature_cards_have_proper_button_layout()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that feature cards have proper button spacing
        $this->assertStringContainsString('d-flex gap-2', $content);
        $this->assertStringContainsString('btn btn-primary btn-sm', $content);
        $this->assertStringContainsString('btn btn-outline-primary btn-sm', $content);
        $this->assertStringContainsString('btn btn-success btn-sm', $content);
        $this->assertStringContainsString('btn btn-outline-success btn-sm', $content);
    }

    #[Test]
    public function client_dashboard_welcome_section_has_proper_icon_spacing()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check welcome section structure with proper spacing
        $this->assertStringContainsString('d-flex align-items-center', $content);
        $this->assertStringContainsString('avatar avatar-lg bg-primary-transparent', $content);
        $this->assertStringContainsString('ti ti-user-check fs-18', $content);
        $this->assertStringContainsString('me-3', $content);
    }

    #[Test]
    public function client_dashboard_access_notice_is_informative()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200)
            ->assertSee('User Access Level')
            ->assertSee('personal profile')
            ->assertSee('account settings')
            ->assertSee('contact your administrator')
            ->assertSee('support button above');
    }

    #[Test]
    public function client_dashboard_support_function_is_included()
    {
        $response = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that support JavaScript function is included
        $this->assertStringContainsString('function contactSupport()', $content);
        $this->assertStringContainsString('onclick="contactSupport()"', $content);
    }

    #[Test]
    public function client_dashboard_maintains_consistency_with_other_dashboards()
    {
        // Test client dashboard
        $clientResponse = $this->actingAs($this->clientUser)
            ->get(route('user.dashboard'));

        // Test member dashboard
        $memberResponse = $this->actingAs($this->member)
            ->get(route('member.dashboard'));

        // Test admin dashboard
        $adminResponse = $this->actingAs($this->adminUser)
            ->get(route('admin.dashboard'));

        // All should return successful responses
        $clientResponse->assertStatus(200);
        $memberResponse->assertStatus(200);
        $adminResponse->assertStatus(200);

        $clientContent = $clientResponse->getContent();
        $memberContent = $memberResponse->getContent();
        $adminContent = $adminResponse->getContent();

        // All should have welcome sections with consistent patterns
        $this->assertStringContainsString('Welcome back,', $clientContent);
        $this->assertStringContainsString('Welcome,', $memberContent);
        $this->assertStringContainsString('Welcome back,', $adminContent);

        // All should use similar card structures
        $this->assertStringContainsString('card custom-card', $clientContent);
        $this->assertStringContainsString('card custom-card', $memberContent);
        $this->assertStringContainsString('card custom-card', $adminContent);

        // Client dashboard should have unique account information section
        $this->assertStringContainsString('Your Account Information', $clientContent);

        // Member and admin dashboards should NOT have account information section (they have different content)
        $this->assertStringNotContainsString('Your Account Information', $memberContent);
        $this->assertStringNotContainsString('Your Account Information', $adminContent);

        // Member and admin dashboards should have reservation tables
        $this->assertStringContainsString('Upcoming reservations this week', $memberContent);
        $this->assertStringContainsString('Upcoming reservations this week', $adminContent);

        // Client dashboard should NOT have reservation tables (different role)
        $this->assertStringNotContainsString('Upcoming reservations this week', $clientContent);
    }
}

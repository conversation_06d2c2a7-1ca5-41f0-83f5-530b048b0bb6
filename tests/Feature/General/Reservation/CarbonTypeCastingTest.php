<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class CarbonTypeCastingTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['role' => 'member']);
        $this->field = Field::factory()->create([
            'name' => 'Test Soccer Field',
            'type' => 'Soccer',
            'hourly_rate' => 75.00,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);
    }

    #[Test]
    public function reservation_creation_handles_string_duration_hours()
    {
        $reservationData = [
            'field_id' => (string) $this->field->id, // String field_id
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00', // 2 hours duration
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Should succeed without Carbon type error
        $response->assertStatus(302); // Redirect on success

        // Verify reservation was created
        $this->assertDatabaseHas('bookings', [
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '12:00', // 10:00 + 2 hours
            'duration_hours' => 2,
            'total_cost' => 150.00, // 75 * 2 hours
        ]);
    }

    #[Test]
    public function ajax_reservation_creation_handles_string_duration_hours()
    {
        $reservationData = [
            'field_id' => (string) $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '17:00', // 3 hours duration
            'customer_name' => 'Jane Doe',
            'customer_email' => '<EMAIL>',
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $reservationData);

        // Should succeed without Carbon type error
        $response->assertStatus(302); // Redirect on success

        // Verify reservation was created with correct end time calculation
        $this->assertDatabaseHas('bookings', [
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'start_time' => '14:00',
            'end_time' => '17:00', // 14:00 + 3 hours
            'duration_hours' => 3,
            'total_cost' => 225.00, // 75 * 3 hours
        ]);
    }

    #[Test]
    public function reservation_update_handles_string_duration_hours()
    {
        // Create initial reservation
        $reservation = $this->actingAs($this->user)
            ->post(route('reservations.store'), [
                'field_id' => $this->field->id,
                'booking_date' => now()->addDays(2)->format('Y-m-d'),
                'start_time' => '09:00',
                'end_time' => '10:00', // 1 hour duration
            ]);

        $reservationId = $this->user->reservations()->first()->id;

        // Update with string field_id and end_time
        $updateData = [
            'field_id' => (string) $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '09:00',
            'end_time' => '13:00', // 4 hours duration
            'customer_name' => 'Updated Name',
        ];

        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservationId), $updateData);

        // Should succeed without Carbon type error
        $response->assertStatus(302); // Redirect on success

        // Verify reservation was updated with correct end time calculation
        $this->assertDatabaseHas('bookings', [
            'id' => $reservationId,
            'start_time' => '09:00',
            'end_time' => '13:00', // 09:00 + 4 hours
            'duration_hours' => 4,
            'total_cost' => 300.00, // 75 * 4 hours
        ]);
    }

    #[Test]
    public function availability_check_handles_string_duration_hours()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.check-availability'), [
                'field_id' => (string) $this->field->id,
                'date' => now()->addDays(1)->format('Y-m-d'),
                'duration_hours' => '2', // String duration_hours
            ]);

        // Should succeed without Carbon type error
        $response->assertStatus(200)
            ->assertJsonStructure([
                'available',
                'message',
                'slots',
            ]);
    }

    #[Test]
    public function cost_estimate_handles_string_parameters()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.cost-estimate'), [
                'field_id' => (string) $this->field->id, // String field_id
                'start_time' => '15:00',
                'end_time' => '18:00', // 3 hours duration
            ]);

        // Should succeed without type errors
        $response->assertStatus(200)
            ->assertJsonStructure([
                'field_name',
                'hourly_rate',
                'duration_hours',
                'total_cost',
                'formatted',
            ]);

        $data = $response->json();
        $this->assertEquals(225.00, $data['total_cost']); // 75 * 3
    }

    #[Test]
    public function working_hours_validation_works_with_string_duration()
    {
        // Try to create reservation that would end after closing time
        $reservationData = [
            'field_id' => (string) $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '20:00', // 8 PM
            'end_time' => '23:00', // 11 PM - after 10 PM closing time
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $reservationData);

        // Should return validation error, not Carbon type error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['start_time']);

        $responseData = $response->json();
        // Just verify we get a validation error, not a Carbon type error
        $this->assertArrayHasKey('start_time', $responseData['errors']);
    }

    #[Test]
    public function duration_validation_works_with_string_duration()
    {
        // Try to create reservation with duration exceeding field maximum
        $reservationData = [
            'field_id' => (string) $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '20:00', // 10 hours - exceeds max of 8 hours
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $reservationData);

        // Should return validation error, not Carbon type error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['end_time']);
    }
}

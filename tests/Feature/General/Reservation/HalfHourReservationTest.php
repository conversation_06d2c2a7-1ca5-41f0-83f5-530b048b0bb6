<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

#[CoversClass(Reservation::class)]
class HalfHourReservationTest extends TestCase
{
    use RefreshDatabase;

    private Field $field;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->create([
            'name' => 'Test Field',
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 75.00,
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8.0,
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);
    }

    #[Test]
    public function test_can_create_half_hour_reservation()
    {
        $this->actingAs($this->user);

        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '10:30',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ]);

        $response->assertRedirect();

        $reservation = Reservation::latest()->first();
        $this->assertEquals(0.5, $reservation->duration_hours);
        $this->assertEquals('10:00', $reservation->start_time);
        $this->assertEquals('10:30', $reservation->end_time);
    }

    #[Test]
    public function test_can_create_one_and_half_hour_reservation()
    {
        $this->actingAs($this->user);

        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '15:30',
            'customer_name' => 'Jane Doe',
            'customer_email' => '<EMAIL>',
        ]);

        $response->assertRedirect();

        $reservation = Reservation::latest()->first();
        $this->assertEquals(1.5, $reservation->duration_hours);
        $this->assertEquals('14:00', $reservation->start_time);
        $this->assertEquals('15:30', $reservation->end_time);
    }

    #[Test]
    public function test_half_hour_cost_calculation()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '10:30',
            'duration_hours' => 0.5,
        ]);

        $cost = $reservation->calculateTotalCost();
        $this->assertEquals(25.00, $cost); // 50.00 * 0.5
    }

    #[Test]
    public function test_one_and_half_hour_cost_calculation()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'start_time' => '14:00',
            'end_time' => '15:30',
            'duration_hours' => 1.5,
        ]);

        $cost = $reservation->calculateTotalCost();
        $this->assertEquals(75.00, $cost); // 50.00 * 1.5
    }

    #[Test]
    public function test_field_generates_half_hour_time_slots()
    {
        $availabilityService = new FieldAvailabilityService;
        $slots = $availabilityService->getAvailableTimeSlots(
            $this->field,
            now()->addDay()->format('Y-m-d'),
            0.5
        );

        $this->assertNotEmpty($slots);

        // Check that we have 30-minute slots
        $firstSlot = $slots[0];
        $this->assertEquals('08:00', $firstSlot['start_time']);
        $this->assertEquals('08:30', $firstSlot['end_time']);

        $secondSlot = $slots[1];
        $this->assertEquals('08:30', $secondSlot['start_time']);
        $this->assertEquals('09:00', $secondSlot['end_time']);
    }

    #[Test]
    public function test_field_validates_half_hour_duration()
    {
        $this->assertTrue($this->field->isValidDuration(0.5));
        $this->assertTrue($this->field->isValidDuration(1.0));
        $this->assertTrue($this->field->isValidDuration(1.5));
        $this->assertTrue($this->field->isValidDuration(2.0));

        // Invalid durations (not half-hour increments)
        $this->assertFalse($this->field->isValidDuration(0.3));
        $this->assertFalse($this->field->isValidDuration(1.2));
        $this->assertFalse($this->field->isValidDuration(2.7));
    }

    #[Test]
    public function test_cost_service_handles_half_hour_durations()
    {
        $costService = new ReservationCostService;

        $cost = $costService->calculateTotalCost($this->field, 0.5, '10:00');
        $this->assertEquals(25.00, $cost);

        $cost = $costService->calculateTotalCost($this->field, 1.5, '10:00');
        $this->assertEquals(75.00, $cost);
    }

    #[Test]
    public function test_validation_rejects_invalid_half_hour_increments()
    {
        $this->actingAs($this->user);

        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '10:18', // Invalid - not a half-hour increment (0.3 hours = 18 minutes)
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ]);

        $response->assertSessionHasErrors('end_time');
    }

    #[Test]
    public function test_availability_check_with_half_hour_duration()
    {
        $this->actingAs($this->user);

        $response = $this->post('/reservations/check-availability', [
            'field_id' => $this->field->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'duration_hours' => 0.5,
        ]);

        $response->assertOk();
        $data = $response->json();

        $this->assertTrue($data['available']);
        $this->assertNotEmpty($data['slots']);

        // Verify slots are 30 minutes apart
        $firstSlot = $data['slots'][0];
        $this->assertEquals('08:00', $firstSlot['start_time']);
        $this->assertEquals('08:30', $firstSlot['end_time']);
    }

    #[Test]
    public function test_get_duration_in_hours_returns_float()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '10:30',
            'duration_hours' => 0.5,
        ]);

        $duration = $reservation->getDurationInHours();
        $this->assertIsFloat($duration);
        $this->assertEquals(0.5, $duration);
    }

    #[Test]
    public function test_three_and_half_hour_cost_calculation()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '13:30',
            'duration_hours' => 3.5,
        ]);

        $cost = $reservation->calculateTotalCost();
        $expectedCost = 50.00 * 3.5; // 175.00
        $this->assertEquals($expectedCost, $cost);
    }

    #[Test]
    public function test_cost_service_handles_three_and_half_hours()
    {
        $costService = new ReservationCostService;

        $cost = $costService->calculateTotalCost($this->field, 3.5, '10:00');
        $expectedCost = 50.00 * 3.5; // 175.00
        $this->assertEquals($expectedCost, $cost);
    }

    #[Test]
    public function test_ajax_cost_estimate_for_three_and_half_hours()
    {
        $this->actingAs($this->user);

        $response = $this->post('/reservations/cost-estimate', [
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '13:30',
            'utilities' => [],
        ]);

        $response->assertOk();
        $data = $response->json();

        $expectedCost = 50.00 * 3.5; // 175.00
        $this->assertEquals($expectedCost, $data['total_cost']);
        $this->assertEquals($expectedCost, $data['subtotal']);
    }

    #[Test]
    public function test_various_decimal_durations_cost_calculation()
    {
        $costService = new ReservationCostService;

        $testCases = [
            ['duration' => 0.5, 'expected' => 25.00],
            ['duration' => 1.5, 'expected' => 75.00],
            ['duration' => 2.5, 'expected' => 125.00],
            ['duration' => 3.5, 'expected' => 175.00],
            ['duration' => 4.5, 'expected' => 225.00],
        ];

        foreach ($testCases as $testCase) {
            $cost = $costService->calculateTotalCost($this->field, $testCase['duration'], '10:00');
            $this->assertEquals(
                $testCase['expected'],
                $cost,
                "Failed for duration {$testCase['duration']} hours"
            );
        }
    }
}

<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReservationsCheckAvailabilityTest extends TestCase
{
    use RefreshDatabase;

    public function test_check_availability_with_hh_mm_ss_times_returns_slots_and_no_500()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Seed a field where opening/closing times use HH:MM:SS
        $field = Field::factory()->create([
            'opening_time' => '08:00:00',
            'closing_time' => '22:00:00',
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8.0,
            'status' => 'Active',
        ]);

        $payload = [
            'field_id' => $field->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'duration_hours' => 1.0,
        ];

        $response = $this->postJson(route('reservations.check-availability'), $payload);

        // Should not be a 500
        $response->assertStatus(200);

        // Should be valid JSON with expected structure
        $response->assertJsonStructure([
            'available',
            'message',
            'slots',
        ]);

        // Slots should be an array (possibly empty if date is in the past or no availability)
        $data = $response->json();
        $this->assertIsArray($data['slots']);
    }

    public function test_get_available_end_times_returns_valid_response()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a field
        $field = Field::factory()->create([
            'opening_time' => '08:00:00',
            'closing_time' => '22:00:00',
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8.0,
            'status' => 'Active',
        ]);

        $payload = [
            'field_id' => $field->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
        ];

        $response = $this->postJson(route('reservations.available-end-times'), $payload);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'end_times',
            'message',
        ]);

        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertIsArray($data['end_times']);

        // Should have at least one end time option (10:30 for 30 min minimum)
        $this->assertGreaterThan(0, count($data['end_times']));

        // Each end time should have the required structure
        if (count($data['end_times']) > 0) {
            $this->assertArrayHasKey('value', $data['end_times'][0]);
            $this->assertArrayHasKey('text', $data['end_times'][0]);
            $this->assertArrayHasKey('duration', $data['end_times'][0]);
        }
    }

    public function test_get_available_end_times_excludes_conflicting_reservations()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a field
        $field = Field::factory()->create([
            'opening_time' => '08:00:00',
            'closing_time' => '22:00:00',
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8.0,
            'status' => 'Active',
        ]);

        $date = now()->addDay()->format('Y-m-d');

        // Create a conflicting reservation from 11:00 to 13:00
        Reservation::factory()->create([
            'field_id' => $field->id,
            'booking_date' => $date,
            'start_time' => '11:00',
            'end_time' => '13:00',
            'status' => 'Confirmed',
            'user_id' => $user->id,
        ]);

        $payload = [
            'field_id' => $field->id,
            'date' => $date,
            'start_time' => '10:00',
        ];

        $response = $this->postJson(route('reservations.available-end-times'), $payload);

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['success']);
        $this->assertIsArray($data['end_times']);

        // Should not include end times that would conflict with the existing reservation
        $endTimeValues = array_column($data['end_times'], 'value');

        // 11:30, 12:00, 12:30, 13:00 should not be available as they would overlap
        $this->assertNotContains('11:30', $endTimeValues);
        $this->assertNotContains('12:00', $endTimeValues);
        $this->assertNotContains('12:30', $endTimeValues);
        $this->assertNotContains('13:00', $endTimeValues);

        // But 10:30 and 11:00 should be available (no overlap)
        $this->assertContains('10:30', $endTimeValues);
        $this->assertContains('11:00', $endTimeValues);
    }

    public function test_get_available_end_times_excludes_current_reservation_when_editing()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a field
        $field = Field::factory()->create([
            'opening_time' => '08:00:00',
            'closing_time' => '22:00:00',
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8.0,
            'status' => 'Active',
        ]);

        $date = now()->addDay()->format('Y-m-d');

        // Create a reservation that we're "editing"
        $currentReservation = Reservation::factory()->create([
            'field_id' => $field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
            'user_id' => $user->id,
        ]);

        $payload = [
            'field_id' => $field->id,
            'date' => $date,
            'start_time' => '10:00',
            'exclude_reservation_id' => $currentReservation->id,
        ];

        $response = $this->postJson(route('reservations.available-end-times'), $payload);

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['success']);
        $this->assertIsArray($data['end_times']);

        // Should include end times that would overlap with the current reservation
        // since we're excluding it from conflict checking
        $endTimeValues = array_column($data['end_times'], 'value');

        // These should be available since we're excluding the current reservation
        $this->assertContains('10:30', $endTimeValues);
        $this->assertContains('11:00', $endTimeValues);
        $this->assertContains('11:30', $endTimeValues);
        $this->assertContains('12:00', $endTimeValues);
    }
}

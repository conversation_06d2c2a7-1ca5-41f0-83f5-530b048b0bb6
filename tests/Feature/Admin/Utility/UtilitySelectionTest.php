<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class UtilitySelectionTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8,
        ]);
    }

    #[Test]
    public function reservation_create_page_displays_active_utilities()
    {
        // Create some utilities
        $activeUtility1 = Utility::factory()->create([
            'name' => 'Projector',
            'hourly_rate' => 15.00,
            'is_active' => true,
        ]);

        $activeUtility2 = Utility::factory()->create([
            'name' => 'Sound System',
            'hourly_rate' => 25.00,
            'is_active' => true,
        ]);

        $inactiveUtility = Utility::factory()->create([
            'name' => 'Inactive Equipment',
            'hourly_rate' => 10.00,
            'is_active' => false,
        ]);

        $this->actingAs($this->user);

        $response = $this->get('/reservations/create');

        $response->assertStatus(200);

        // Check that active utilities are passed to the view
        $response->assertViewHas('utilities');
        $utilities = $response->viewData('utilities');

        $this->assertCount(2, $utilities);
        $this->assertTrue($utilities->contains('id', $activeUtility1->id));
        $this->assertTrue($utilities->contains('id', $activeUtility2->id));
        $this->assertFalse($utilities->contains('id', $inactiveUtility->id));

        // Check that utilities are rendered in the HTML
        $response->assertSee('Projector');
        $response->assertSee('Sound System');
        $response->assertSee('XCG 15.00');
        $response->assertSee('XCG 25.00');
        $response->assertDontSee('Inactive Equipment');
    }

    #[Test]
    public function reservation_create_page_shows_empty_utilities_when_none_active()
    {
        // Create only inactive utilities
        Utility::factory()->create([
            'name' => 'Inactive Equipment',
            'hourly_rate' => 10.00,
            'is_active' => false,
        ]);

        $this->actingAs($this->user);

        $response = $this->get('/reservations/create');

        $response->assertStatus(200);

        // Check that no utilities are passed to the view
        $response->assertViewHas('utilities');
        $utilities = $response->viewData('utilities');

        $this->assertCount(0, $utilities);

        // Should still show the utility selection interface but with no options
        $response->assertSee('Select Utility');
        $response->assertDontSee('Inactive Equipment');
    }

    #[Test]
    public function utility_selection_interface_is_present_in_form()
    {
        // Create an active utility
        Utility::factory()->create([
            'name' => 'Test Utility',
            'hourly_rate' => 20.00,
            'is_active' => true,
        ]);

        $this->actingAs($this->user);

        $response = $this->get('/reservations/create');

        $response->assertStatus(200);

        // Check for utility selection elements
        $response->assertSee('Add Utility');
        $response->assertSee('utilitySelect');
        $response->assertSee('utilityQuantity');
        $response->assertSee('addUtilityBtn');
        $response->assertSee('Complete booking details first');
        $response->assertSee('Select Utility');
    }

    #[Test]
    public function utilities_are_properly_formatted_in_dropdown()
    {
        // Create utilities with specific rates
        $utility1 = Utility::factory()->create([
            'name' => 'Expensive Equipment',
            'hourly_rate' => 123.45,
            'is_active' => true,
        ]);

        $utility2 = Utility::factory()->create([
            'name' => 'Cheap Equipment',
            'hourly_rate' => 5.00,
            'is_active' => true,
        ]);

        $this->actingAs($this->user);

        $response = $this->get('/reservations/create');

        $response->assertStatus(200);

        // Check that rates are properly formatted
        $response->assertSee('Expensive Equipment');
        $response->assertSee('XCG 123.45');
        $response->assertSee('Cheap Equipment');
        $response->assertSee('XCG 5.00');

        // Check that utility options are present in the select
        $response->assertSee('value="'.$utility1->id.'"', false);
        $response->assertSee('value="'.$utility2->id.'"', false);
    }
}

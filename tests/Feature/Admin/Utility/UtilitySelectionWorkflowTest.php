<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class UtilitySelectionWorkflowTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Field $field;

    private Utility $projector;

    private Utility $table;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8,
        ]);

        $this->projector = Utility::factory()->create([
            'name' => 'Projector',
            'hourly_rate' => 15.00,
            'is_active' => true,
        ]);

        $this->table = Utility::factory()->create([
            'name' => 'Table',
            'hourly_rate' => 5.00,
            'is_active' => true,
        ]);
    }

    #[Test]
    public function user_can_create_reservation_with_utilities_using_new_quantity_system()
    {
        $this->actingAs($this->user);

        // Test the complete workflow: create reservation with utilities
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:30', // 2.5 hours duration
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $this->projector->id,
                    'hours' => 2, // 2 projectors (quantity)
                ],
                [
                    'id' => $this->table->id,
                    'hours' => 3, // 3 tables (quantity)
                ],
            ],
        ];

        $response = $this->post('/reservations', $reservationData);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();

        // Verify reservation was created
        $reservation = Reservation::where('user_id', $this->user->id)->first();
        $this->assertNotNull($reservation);

        // Verify field duration
        $this->assertEquals(2.5, $reservation->duration_hours);

        // Verify utilities were attached with correct costs
        $this->assertEquals(2, $reservation->utilities->count());

        $projectorUtility = $reservation->utilities->where('id', $this->projector->id)->first();
        $this->assertNotNull($projectorUtility);
        $this->assertEquals(2, $projectorUtility->pivot->hours); // quantity
        $this->assertEquals(30.00, $projectorUtility->pivot->cost); // 15 * 2 (quantity only, not multiplied by duration)

        $tableUtility = $reservation->utilities->where('id', $this->table->id)->first();
        $this->assertNotNull($tableUtility);
        $this->assertEquals(3, $tableUtility->pivot->hours); // quantity
        $this->assertEquals(15.00, $tableUtility->pivot->cost); // 5 * 3 (quantity only, not multiplied by duration)

        // Verify total cost calculation
        $expectedFieldCost = 50.00 * 2.5; // 125.00
        $expectedUtilityCost = 30.00 + 15.00; // 45.00
        $expectedTotalCost = $expectedFieldCost + $expectedUtilityCost; // 170.00

        $this->assertEquals($expectedTotalCost, $reservation->total_cost);
    }

    #[Test]
    public function cost_estimation_endpoint_works_with_utilities()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/reservations/cost-estimate', [
            'field_id' => $this->field->id,
            'start_time' => '14:00',
            'end_time' => '15:30',
            'utilities' => [
                ['id' => $this->projector->id, 'hours' => 1], // 1 projector
                ['id' => $this->table->id, 'hours' => 2], // 2 tables
            ],
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        // Check if the response has the expected structure
        if (isset($data['error'])) {
            $this->fail('API returned error: '.$data['error']);
        }

        // Expected calculations:
        // Field: 50 * 1.5 = 75.00
        // Projector: 15 * 1 = 15.00 (quantity only, not multiplied by duration)
        // Tables: 5 * 2 = 10.00 (quantity only, not multiplied by duration)
        // Total utility: 15.00 + 10.00 = 25.00
        // Total: 75.00 + 25.00 = 100.00

        $this->assertEquals(75.00, $data['field_cost']);
        $this->assertEquals(25.00, $data['utility_cost']);
        $this->assertEquals(100.00, $data['total_cost']);

        // Check utility breakdown
        $this->assertCount(2, $data['utility_breakdown']);

        $projectorBreakdown = collect($data['utility_breakdown'])->firstWhere('utility_id', $this->projector->id);
        $this->assertEquals(1, $projectorBreakdown['hours']);
        $this->assertEquals(15.00, $projectorBreakdown['cost']);

        $tableBreakdown = collect($data['utility_breakdown'])->firstWhere('utility_id', $this->table->id);
        $this->assertEquals(2, $tableBreakdown['hours']);
        $this->assertEquals(10.00, $tableBreakdown['cost']);
    }

    #[Test]
    public function utility_validation_rejects_decimal_quantities()
    {
        $this->actingAs($this->user);

        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:30', // Field duration can be decimal (1.5 hours)
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $this->projector->id,
                    'hours' => 1.5, // This should be rejected (decimal quantity)
                ],
            ],
        ]);

        $response->assertSessionHasErrors('utilities.0.hours');
    }

    #[Test]
    public function utility_validation_accepts_whole_number_quantities()
    {
        $this->actingAs($this->user);

        $response = $this->post('/reservations', [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:30', // Field duration can be decimal (1.5 hours)
            'customer_name' => 'Test Customer',
            'utilities' => [
                [
                    'id' => $this->projector->id,
                    'hours' => 2, // This should be accepted (whole number quantity)
                ],
            ],
        ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }

    #[Test]
    public function reservation_create_page_loads_with_utilities_available()
    {
        $this->actingAs($this->user);

        $response = $this->get('/reservations/create');

        $response->assertStatus(200);
        $response->assertViewHas('utilities');

        $utilities = $response->viewData('utilities');
        $this->assertCount(2, $utilities);
        $this->assertTrue($utilities->contains('id', $this->projector->id));
        $this->assertTrue($utilities->contains('id', $this->table->id));

        // Check that the utility selection interface is present
        $response->assertSee('Add Utility');
        $response->assertSee('Projector');
        $response->assertSee('Table');
        $response->assertSee('XCG 15.00');
        $response->assertSee('XCG 5.00');
    }
}

<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class UtilitySelectionFunctionalTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function complete_utility_selection_workflow_demonstration()
    {
        // Setup: Create user, field, and utilities
        $user = User::factory()->create();
        $field = Field::factory()->create([
            'name' => 'Main Soccer Field',
            'hourly_rate' => 50.00,
            'min_booking_hours' => 0.5,
            'max_booking_hours' => 8,
        ]);

        $projector = Utility::create([
            'name' => 'HD Projector',
            'description' => 'High-definition projector for presentations',
            'hourly_rate' => 15.00,
            'icon_class' => 'ti ti-device-projector',
            'is_active' => true,
        ]);

        $soundSystem = Utility::create([
            'name' => 'Sound System',
            'description' => 'Professional sound system with wireless microphones',
            'hourly_rate' => 25.00,
            'icon_class' => 'ti ti-volume',
            'is_active' => true,
        ]);

        $table = Utility::create([
            'name' => 'Folding Table',
            'description' => 'Portable folding table for events',
            'hourly_rate' => 5.00,
            'icon_class' => 'ti ti-table',
            'is_active' => true,
        ]);

        // Create an inactive utility that should not appear
        $inactiveUtility = Utility::create([
            'name' => 'Broken Equipment',
            'description' => 'Out of service equipment',
            'hourly_rate' => 10.00,
            'icon_class' => 'ti ti-x',
            'is_active' => false,
        ]);

        $this->actingAs($user);

        // Step 1: Test that the reservation create page loads with utilities
        $response = $this->get('/reservations/create');
        $response->assertStatus(200);

        // Verify utilities are passed to the view
        $response->assertViewHas('utilities');
        $utilities = $response->viewData('utilities');
        $this->assertCount(3, $utilities); // Only active utilities

        // Verify active utilities are displayed
        $response->assertSee('HD Projector');
        $response->assertSee('Sound System');
        $response->assertSee('Folding Table');
        $response->assertSee('XCG 15.00');
        $response->assertSee('XCG 25.00');
        $response->assertSee('XCG 5.00');

        // Verify inactive utility is not displayed
        $response->assertDontSee('Broken Equipment');

        // Step 2: Test cost estimation with utilities
        $costResponse = $this->postJson('/reservations/cost-estimate', [
            'field_id' => $field->id,
            'start_time' => '14:00',
            'end_time' => '16:00',
            'utilities' => [
                ['id' => $projector->id, 'hours' => 1], // 1 projector
                ['id' => $soundSystem->id, 'hours' => 1], // 1 sound system
                ['id' => $table->id, 'hours' => 4], // 4 tables
            ],
        ]);

        $costResponse->assertStatus(200);
        $costData = $costResponse->json();

        // Check if the response has the expected structure
        if (isset($costData['error'])) {
            $this->fail('API returned error: '.$costData['error']);
        }

        // Expected calculations:
        // Field: 50 * 2 = 100.00
        // Projector: 15 * 1 = 15.00 (quantity only, not multiplied by duration)
        // Sound System: 25 * 1 = 25.00 (quantity only, not multiplied by duration)
        // Tables: 5 * 4 = 20.00 (quantity only, not multiplied by duration)
        // Total utility: 15 + 25 + 20 = 60.00
        // Total: 100 + 60 = 160.00

        $this->assertEquals(100.00, $costData['field_cost']);
        $this->assertEquals(60.00, $costData['utility_cost']);
        $this->assertEquals(160.00, $costData['total_cost']);
        $this->assertCount(3, $costData['utility_breakdown']);

        // Step 3: Test creating a reservation with utilities
        $reservationResponse = $this->post('/reservations', [
            'field_id' => $field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '14:00',
            'end_time' => '16:00',
            'customer_name' => 'Event Organizer',
            'customer_email' => '<EMAIL>',
            'utilities' => [
                ['id' => $projector->id, 'hours' => 1],
                ['id' => $soundSystem->id, 'hours' => 1],
                ['id' => $table->id, 'hours' => 4],
            ],
        ]);

        $reservationResponse->assertRedirect();
        $reservationResponse->assertSessionHasNoErrors();

        // Step 4: Verify the reservation was created correctly
        $reservation = Reservation::where('user_id', $user->id)->first();
        $this->assertNotNull($reservation);
        $this->assertEquals(160.00, $reservation->total_cost);
        $this->assertEquals(3, $reservation->utilities->count());

        // Verify individual utility costs
        $reservationProjector = $reservation->utilities->where('id', $projector->id)->first();
        $this->assertEquals(1, $reservationProjector->pivot->hours);
        $this->assertEquals(15.00, $reservationProjector->pivot->cost);

        $reservationSound = $reservation->utilities->where('id', $soundSystem->id)->first();
        $this->assertEquals(1, $reservationSound->pivot->hours);
        $this->assertEquals(25.00, $reservationSound->pivot->cost);

        $reservationTables = $reservation->utilities->where('id', $table->id)->first();
        $this->assertEquals(4, $reservationTables->pivot->hours);
        $this->assertEquals(20.00, $reservationTables->pivot->cost);

        // Step 5: Test validation - decimal quantities should be rejected
        $invalidResponse = $this->post('/reservations', [
            'field_id' => $field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:30',
            'customer_name' => 'Invalid Customer',
            'utilities' => [
                ['id' => $projector->id, 'hours' => 1.5], // Invalid decimal quantity
            ],
        ]);

        $invalidResponse->assertSessionHasErrors('utilities.0.hours');

        // Step 6: Test that field duration can still be decimal
        $validDecimalResponse = $this->post('/reservations', [
            'field_id' => $field->id,
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:30', // Valid decimal field duration (1.5 hours)
            'customer_name' => 'Valid Customer',
            'utilities' => [
                ['id' => $projector->id, 'hours' => 2], // Valid whole number quantity
            ],
        ]);

        $validDecimalResponse->assertRedirect();
        $validDecimalResponse->assertSessionHasNoErrors();

        // Verify the decimal duration reservation
        $decimalReservation = Reservation::where('customer_name', 'Valid Customer')->first();
        $this->assertNotNull($decimalReservation);
        $this->assertEquals(1.5, $decimalReservation->duration_hours);

        $decimalProjector = $decimalReservation->utilities->where('id', $projector->id)->first();
        $this->assertEquals(2, $decimalProjector->pivot->hours); // quantity
        $this->assertEquals(30.00, $decimalProjector->pivot->cost); // 15 * 2 (quantity only, not multiplied by duration)

        // Final verification: Total reservations created
        $this->assertEquals(2, Reservation::count());

        echo "\n✅ Utility Selection Functionality Test Complete!\n";
        echo "✅ Utilities are properly displayed in the reservation form\n";
        echo "✅ Cost calculation works with new quantity system\n";
        echo "✅ Reservations can be created with utilities\n";
        echo "✅ Validation correctly rejects decimal quantities\n";
        echo "✅ Field duration still supports half-hour increments\n";
    }
}

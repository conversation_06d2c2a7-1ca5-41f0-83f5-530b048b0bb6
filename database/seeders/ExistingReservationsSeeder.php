<?php

namespace Database\Seeders;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ExistingReservationsSeeder extends Seeder
{
    /**
     * Predefined customer names to use for reservations
     */
    private array $customerNames = [
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
    ];

    /**
     * Run the database seeds.
     *
     * This seeder exports existing reservation data and updates dates to start from today,
     * maintaining relative time intervals between reservations.
     */
    public function run(): void
    {
        $this->command->info('Starting ExistingReservationsSeeder...');

        // Clear existing reservations if running fresh
        // if ($this->command->confirm('Do you want to clear existing reservations before seeding?', false)) {
        //     $this->command->info('Clearing existing reservations...');
        //     DB::table('reservation_utility')->delete();
        //     Reservation::query()->delete();
        //     $this->command->info('Existing reservations cleared.');
        // }

        $this->command->info('Clearing existing reservations...');
        DB::table('reservation_utility')->delete();
        Reservation::query()->delete();
        $this->command->info('Existing reservations cleared.');

        // Define the reservation data based on current database state
        $reservationsData = $this->getReservationsData();

        if (empty($reservationsData)) {
            $this->command->warn('No reservation data found to seed.');

            return;
        }

        // Calculate date adjustments to move all dates forward starting from today
        $originalDates = collect($reservationsData)->pluck('booking_date')->unique()->sort()->values();
        $earliestDate = Carbon::parse($originalDates->first());
        $today = Carbon::today();

        // Calculate how many days to add to move the earliest date to today
        // If earliest date is in the past, we need to add days to bring it to today
        // If earliest date is in the future, we need to subtract days to bring it to today
        $daysDifference = $earliestDate->diffInDays($today, false);

        $this->command->info("Adjusting dates: earliest was {$earliestDate->format('Y-m-d')}, now starting from {$today->format('Y-m-d')}");
        $this->command->info("Date adjustment: {$daysDifference} days");

        // Create reservations with updated dates
        foreach ($reservationsData as $reservationData) {
            $this->createReservation($reservationData, $daysDifference);
        }

        $this->command->info('ExistingReservationsSeeder completed successfully!');
        $this->command->info('Created '.count($reservationsData).' reservations with updated dates.');
    }

    /**
     * Get the existing reservations data structure
     * This data is based on the current state of the database as of August 2025
     */
    private function getReservationsData(): array
    {
        // Base reservation data template
        $baseReservations = [
            [
                'field_name' => 'Veld futbol',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-10',
                'start_time' => '15:00',
                'end_time' => '16:00',
                'duration_hours' => 1.0,
                'total_cost' => 65.00,
                'status' => 'Completed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:25:50',
                'cancelled_at' => null,
                'utilities' => [
                    ['name' => 'Paña di mesa', 'hours' => 1, 'rate' => 15.00, 'cost' => 15.00],
                ],
            ],
            [
                'field_name' => 'Veld futbol',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-15',
                'start_time' => '14:30',
                'end_time' => '16:00',
                'duration_hours' => 1.5,
                'total_cost' => 75.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:26:32',
                'cancelled_at' => null,
                'utilities' => [
                    ['name' => 'Sta tafel', 'hours' => 1, 'rate' => 15.00, 'cost' => 15.00],
                ],
            ],
            [
                'field_name' => 'Veld Bolas',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-19',
                'start_time' => '10:30',
                'end_time' => '12:30',
                'duration_hours' => 2.0,
                'total_cost' => 100.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:27:07',
                'cancelled_at' => null,
                'utilities' => [],
            ],
            [
                'field_name' => 'Veld Bolas',
                'user_email' => '<EMAIL>',
                'booked_by_email' => '<EMAIL>', // Admin-booked reservation
                'booking_date' => '2025-08-17',
                'start_time' => '12:00',
                'end_time' => '13:30',
                'duration_hours' => 1.5,
                'total_cost' => 75.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => 'Admin booked for member',
                'admin_notes' => 'Booked by admin on behalf of member',
                'confirmed_at' => '2025-08-09 00:27:33',
                'cancelled_at' => null,
                'utilities' => [],
            ],
            [
                'field_name' => 'Veld futbol',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-11',
                'start_time' => '15:00',
                'end_time' => '18:30',
                'duration_hours' => 3.5,
                'total_cost' => 255.00,
                'status' => 'Completed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:28:12',
                'cancelled_at' => null,
                'utilities' => [
                    ['name' => 'Mesa rondo', 'hours' => 1, 'rate' => 15.00, 'cost' => 15.00],
                ],
            ],
            [
                'field_name' => 'Veld futbol',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-15',
                'start_time' => '15:30',
                'end_time' => '16:30',
                'duration_hours' => 1.0,
                'total_cost' => 50.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:28:45',
                'cancelled_at' => null,
                'utilities' => [],
            ],
            [
                'field_name' => 'Veld Multi',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-15',
                'start_time' => '18:30',
                'end_time' => '21:30',
                'duration_hours' => 3.0,
                'total_cost' => 240.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:29:18',
                'cancelled_at' => null,
                'utilities' => [],
            ],
            [
                'field_name' => 'Veld Multi',
                'user_email' => '<EMAIL>',
                'booked_by_email' => '<EMAIL>', // Admin-booked reservation
                'booking_date' => '2025-08-15',
                'start_time' => '15:30',
                'end_time' => '17:00',
                'duration_hours' => 1.5,
                'total_cost' => 75.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => 'Half-hour extension requested',
                'admin_notes' => 'Extended booking by 30 minutes per member request',
                'confirmed_at' => '2025-08-09 00:29:51',
                'cancelled_at' => null,
                'utilities' => [],
            ],
            [
                'field_name' => 'Patio Area',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-18',
                'start_time' => '16:00',
                'end_time' => '18:00',
                'duration_hours' => 2.0,
                'total_cost' => 130.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:30:24',
                'cancelled_at' => null,
                'utilities' => [
                    ['name' => 'Mesa rondo', 'hours' => 1, 'rate' => 15.00, 'cost' => 15.00],
                    ['name' => 'Paña di mesa', 'hours' => 2, 'rate' => 15.00, 'cost' => 30.00],
                    ['name' => 'Stoel', 'hours' => 10, 'rate' => 1.50, 'cost' => 15.00],
                ],
            ],
            [
                'field_name' => 'Patio Area',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-20',
                'start_time' => '18:00',
                'end_time' => '20:00',
                'duration_hours' => 2.0,
                'total_cost' => 130.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:30:57',
                'cancelled_at' => null,
                'utilities' => [
                    ['name' => 'Sta tafel', 'hours' => 2, 'rate' => 15.00, 'cost' => 30.00],
                    ['name' => 'Mesa rondo', 'hours' => 3, 'rate' => 15.00, 'cost' => 45.00],
                    ['name' => 'Stoel', 'hours' => 100, 'rate' => 1.50, 'cost' => 150.00],
                ],
            ],
            [
                'field_name' => 'Patio Area',
                'user_email' => '<EMAIL>',
                'booked_by_email' => null, // Self-booked by user
                'booking_date' => '2025-08-20',
                'start_time' => '14:00',
                'end_time' => '18:00',
                'duration_hours' => 4.0,
                'total_cost' => 100.00,
                'status' => 'Confirmed',
                'customer_phone' => '+59990000000',
                'special_requests' => null,
                'admin_notes' => null,
                'confirmed_at' => '2025-08-09 00:31:30',
                'cancelled_at' => null,
                'utilities' => [],
            ],
        ];

        // Generate reservations with random customer names
        $reservations = [];
        foreach ($baseReservations as $reservation) {
            $customerName = fake()->randomElement($this->customerNames);
            $customerEmail = str_replace(' ', '_', strtolower($customerName)).'@example.com';

            $reservation['customer_name'] = $customerName;
            $reservation['customer_email'] = $customerEmail;

            $reservations[] = $reservation;
        }

        return $reservations;
    }

    /**
     * Create a reservation with adjusted date and attach utilities
     */
    private function createReservation(array $data, int $daysDifference): void
    {
        // Find the field and user
        $field = Field::where('name', $data['field_name'])->first();
        $user = User::where('email', $data['user_email'])->first();

        if (! $field) {
            $this->command->warn("Field '{$data['field_name']}' not found. Skipping reservation.");

            return;
        }

        if (! $user) {
            $this->command->warn("User '{$data['user_email']}' not found. Skipping reservation.");

            return;
        }

        // Find the booked_by user if specified
        $bookedBy = null;
        if (isset($data['booked_by_email']) && $data['booked_by_email']) {
            $bookedBy = User::where('email', $data['booked_by_email'])->first();
            if (! $bookedBy) {
                $this->command->warn("Booked by user '{$data['booked_by_email']}' not found. Setting to null.");
            }
        }

        // Adjust the booking date
        $originalDate = Carbon::parse($data['booking_date']);
        $adjustedDate = $originalDate->addDays($daysDifference);

        // Adjust confirmed_at date if it exists
        $confirmedAt = null;
        if ($data['confirmed_at']) {
            $originalConfirmedAt = Carbon::parse($data['confirmed_at']);
            $confirmedAt = $originalConfirmedAt->addDays($daysDifference);
        }

        // Adjust cancelled_at date if it exists
        $cancelledAt = null;
        if ($data['cancelled_at']) {
            $originalCancelledAt = Carbon::parse($data['cancelled_at']);
            $cancelledAt = $originalCancelledAt->addDays($daysDifference);
        }

        // Check for existing reservation to avoid duplicates
        $existingReservation = Reservation::where([
            'field_id' => $field->id,
            'user_id' => $user->id,
            'booking_date' => $adjustedDate->format('Y-m-d'),
            'start_time' => $data['start_time'],
            'end_time' => $data['end_time'],
        ])->first();

        if ($existingReservation) {
            $this->command->warn("Reservation already exists for {$field->name} on {$adjustedDate->format('Y-m-d')} at {$data['start_time']}. Skipping.");

            return;
        }

        // Create the reservation with all available fields
        $reservationData = [
            'field_id' => $field->id,
            'user_id' => $user->id,
            'booking_date' => $adjustedDate->format('Y-m-d'),
            'start_time' => $data['start_time'],
            'end_time' => $data['end_time'],
            'duration_hours' => $data['duration_hours'],
            'total_cost' => $data['total_cost'],
            'status' => $data['status'],
            'customer_name' => $data['customer_name'],
            'customer_email' => $data['customer_email'],
            'customer_phone' => $data['customer_phone'],
            'special_requests' => $data['special_requests'],
            'confirmed_at' => $confirmedAt,
            'cancelled_at' => $cancelledAt,
        ];

        // Add booked_by if available (this field exists in database but not in model fillable)
        if ($bookedBy) {
            $reservationData['booked_by'] = $bookedBy->id;
        }

        // Add admin_notes if available (this field exists in database but not in model fillable)
        if (isset($data['admin_notes'])) {
            $reservationData['admin_notes'] = $data['admin_notes'];
        }

        $reservation = Reservation::create($reservationData);

        // Attach utilities if any
        if (! empty($data['utilities'])) {
            foreach ($data['utilities'] as $utilityData) {
                $utility = Utility::where('name', $utilityData['name'])->first();

                if ($utility) {
                    $reservation->utilities()->attach($utility->id, [
                        'hours' => $utilityData['hours'],
                        'rate' => $utilityData['rate'],
                        'cost' => $utilityData['cost'],
                    ]);
                } else {
                    $this->command->warn("Utility '{$utilityData['name']}' not found for reservation {$reservation->id}.");
                }
            }
        }

        $this->command->info("Created reservation: {$field->name} on {$adjustedDate->format('Y-m-d')} at {$data['start_time']}-{$data['end_time']} for {$user->name}");
    }
}

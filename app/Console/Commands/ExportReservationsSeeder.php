<?php

namespace App\Console\Commands;

use App\Models\Reservation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ExportReservationsSeeder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reservations:export-seeder {--file=ExistingReservationsSeeder.php}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export current reservations to a seeder file with updated dates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Exporting reservations to seeder...');

        $reservations = Reservation::with(['field', 'user', 'utilities'])->get();

        if ($reservations->isEmpty()) {
            $this->warn('No reservations found to export.');

            return;
        }

        $seederContent = $this->generateSeederContent($reservations);
        $filename = $this->option('file');
        $filepath = database_path("seeders/{$filename}");

        File::put($filepath, $seederContent);

        $this->info("Seeder exported to: {$filepath}");
        $this->info("Total reservations exported: {$reservations->count()}");
    }

    /**
     * Generate the seeder file content
     */
    private function generateSeederContent($reservations): string
    {
        $reservationsData = [];

        foreach ($reservations as $reservation) {
            $utilities = [];
            foreach ($reservation->utilities as $utility) {
                $utilities[] = [
                    'name' => $utility->name,
                    'hours' => $utility->pivot->hours,
                    'rate' => (float) $utility->pivot->rate,
                    'cost' => (float) $utility->pivot->cost,
                ];
            }

            // Handle booked_by relationship
            $bookedByEmail = null;
            if ($reservation->booked_by) {
                $bookedByUser = \App\Models\User::find($reservation->booked_by);
                $bookedByEmail = $bookedByUser ? $bookedByUser->email : null;
            }

            $reservationsData[] = [
                'field_name' => $reservation->field->name,
                'user_email' => $reservation->user->email,
                'booked_by_email' => $bookedByEmail,
                'booking_date' => $reservation->booking_date->format('Y-m-d'),
                'start_time' => $reservation->start_time,
                'end_time' => $reservation->end_time,
                'duration_hours' => (float) $reservation->duration_hours,
                'total_cost' => (float) $reservation->total_cost,
                'status' => $reservation->status,
                'customer_name' => $reservation->customer_name,
                'customer_email' => $reservation->customer_email,
                'customer_phone' => $reservation->customer_phone,
                'special_requests' => $reservation->special_requests,
                'admin_notes' => $reservation->admin_notes,
                'confirmed_at' => $reservation->confirmed_at?->format('Y-m-d H:i:s'),
                'cancelled_at' => $reservation->cancelled_at?->format('Y-m-d H:i:s'),
                'utilities' => $utilities,
            ];
        }

        return $this->buildSeederFileContent($reservationsData);
    }

    /**
     * Build the complete seeder file content
     */
    private function buildSeederFileContent(array $reservationsData): string
    {
        $dataString = var_export($reservationsData, true);

        // Clean up the exported array format for better readability
        $dataString = str_replace('array (', '[', $dataString);
        $dataString = str_replace(')', ']', $dataString);
        $dataString = preg_replace('/\s+\d+\s+=>\s+/', '', $dataString);

        return <<<PHP
<?php

namespace Database\Seeders;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ExistingReservationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * This seeder exports existing reservation data and updates dates to start from today,
     * maintaining relative time intervals between reservations.
     * 
     * Generated automatically from existing database data.
     */
    public function run(): void
    {
        \$this->command->info('Starting ExistingReservationsSeeder...');

        // Clear existing reservations if running fresh
        if (\$this->command->confirm('Do you want to clear existing reservations before seeding?', false)) {
            \$this->command->info('Clearing existing reservations...');
            DB::table('reservation_utility')->delete();
            Reservation::query()->delete();
            \$this->command->info('Existing reservations cleared.');
        }

        // Define the reservation data based on current database state
        \$reservationsData = \$this->getReservationsData();

        if (empty(\$reservationsData)) {
            \$this->command->warn('No reservation data found to seed.');
            return;
        }

        // Calculate date adjustments
        \$originalDates = collect(\$reservationsData)->pluck('booking_date')->unique()->sort()->values();
        \$earliestDate = Carbon::parse(\$originalDates->first());
        \$today = Carbon::today();
        \$daysDifference = \$today->diffInDays(\$earliestDate, false);

        \$this->command->info("Adjusting dates: earliest was {\$earliestDate->format('Y-m-d')}, now starting from {\$today->format('Y-m-d')}");
        \$this->command->info("Date adjustment: {\$daysDifference} days");

        // Create reservations with updated dates
        foreach (\$reservationsData as \$reservationData) {
            \$this->createReservation(\$reservationData, \$daysDifference);
        }

        \$this->command->info('ExistingReservationsSeeder completed successfully!');
        \$this->command->info('Created ' . count(\$reservationsData) . ' reservations with updated dates.');
    }

    /**
     * Get the existing reservations data structure
     * This data was exported from the database automatically
     */
    private function getReservationsData(): array
    {
        return {$dataString};
    }

    /**
     * Create a reservation with adjusted date and attach utilities
     */
    private function createReservation(array \$data, int \$daysDifference): void
    {
        // Find the field and user
        \$field = Field::where('name', \$data['field_name'])->first();
        \$user = User::where('email', \$data['user_email'])->first();

        if (!\$field) {
            \$this->command->warn("Field '{\$data['field_name']}' not found. Skipping reservation.");
            return;
        }

        if (!\$user) {
            \$this->command->warn("User '{\$data['user_email']}' not found. Skipping reservation.");
            return;
        }

        // Find the booked_by user if specified
        \$bookedBy = null;
        if (isset(\$data['booked_by_email']) && \$data['booked_by_email']) {
            \$bookedBy = User::where('email', \$data['booked_by_email'])->first();
            if (!\$bookedBy) {
                \$this->command->warn("Booked by user '{\$data['booked_by_email']}' not found. Setting to null.");
            }
        }

        // Adjust the booking date
        \$originalDate = Carbon::parse(\$data['booking_date']);
        \$adjustedDate = \$originalDate->addDays(\$daysDifference);

        // Adjust confirmed_at date if it exists
        \$confirmedAt = null;
        if (\$data['confirmed_at']) {
            \$originalConfirmedAt = Carbon::parse(\$data['confirmed_at']);
            \$confirmedAt = \$originalConfirmedAt->addDays(\$daysDifference);
        }

        // Adjust cancelled_at date if it exists
        \$cancelledAt = null;
        if (\$data['cancelled_at']) {
            \$originalCancelledAt = Carbon::parse(\$data['cancelled_at']);
            \$cancelledAt = \$originalCancelledAt->addDays(\$daysDifference);
        }

        // Check for existing reservation to avoid duplicates
        \$existingReservation = Reservation::where([
            'field_id' => \$field->id,
            'user_id' => \$user->id,
            'booking_date' => \$adjustedDate->format('Y-m-d'),
            'start_time' => \$data['start_time'],
            'end_time' => \$data['end_time'],
        ])->first();

        if (\$existingReservation) {
            \$this->command->warn("Reservation already exists for {\$field->name} on {\$adjustedDate->format('Y-m-d')} at {\$data['start_time']}. Skipping.");
            return;
        }

        // Create the reservation with all available fields
        \$reservationData = [
            'field_id' => \$field->id,
            'user_id' => \$user->id,
            'booking_date' => \$adjustedDate->format('Y-m-d'),
            'start_time' => \$data['start_time'],
            'end_time' => \$data['end_time'],
            'duration_hours' => \$data['duration_hours'],
            'total_cost' => \$data['total_cost'],
            'status' => \$data['status'],
            'customer_name' => \$data['customer_name'],
            'customer_email' => \$data['customer_email'],
            'customer_phone' => \$data['customer_phone'],
            'special_requests' => \$data['special_requests'],
            'confirmed_at' => \$confirmedAt,
            'cancelled_at' => \$cancelledAt,
        ];

        // Add booked_by if available (this field exists in database but not in model fillable)
        if (\$bookedBy) {
            \$reservationData['booked_by'] = \$bookedBy->id;
        }

        // Add admin_notes if available (this field exists in database but not in model fillable)
        if (isset(\$data['admin_notes'])) {
            \$reservationData['admin_notes'] = \$data['admin_notes'];
        }

        \$reservation = Reservation::create(\$reservationData);

        // Attach utilities if any
        if (!empty(\$data['utilities'])) {
            foreach (\$data['utilities'] as \$utilityData) {
                \$utility = Utility::where('name', \$utilityData['name'])->first();
                
                if (\$utility) {
                    \$reservation->utilities()->attach(\$utility->id, [
                        'hours' => \$utilityData['hours'],
                        'rate' => \$utilityData['rate'],
                        'cost' => \$utilityData['cost'],
                    ]);
                } else {
                    \$this->command->warn("Utility '{\$utilityData['name']}' not found for reservation {\$reservation->id}.");
                }
            }
        }

        \$this->command->info("Created reservation: {\$field->name} on {\$adjustedDate->format('Y-m-d')} at {\$data['start_time']}-{\$data['end_time']} for {\$user->name}");
    }
}
PHP;
    }
}
